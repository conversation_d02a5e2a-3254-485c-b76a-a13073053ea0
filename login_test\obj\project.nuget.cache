{"version": 2, "dgSpecHash": "7A7r5InxzMA=", "success": true, "projectFilePath": "H:\\gold\\gold\\login_test\\CompleteLoginTest.csproj", "expectedPackageFiles": ["H:\\gold\\nuget-packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.netanalyzers\\8.0.0\\microsoft.codeanalysis.netanalyzers.8.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration\\9.0.7\\microsoft.extensions.configuration.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.abstractions\\9.0.7\\microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.binder\\9.0.7\\microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.commandline\\9.0.7\\microsoft.extensions.configuration.commandline.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.environmentvariables\\9.0.7\\microsoft.extensions.configuration.environmentvariables.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.fileextensions\\9.0.7\\microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.json\\9.0.7\\microsoft.extensions.configuration.json.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.usersecrets\\9.0.7\\microsoft.extensions.configuration.usersecrets.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.dependencyinjection\\9.0.7\\microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.7\\microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics\\9.0.7\\microsoft.extensions.diagnostics.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics.abstractions\\9.0.7\\microsoft.extensions.diagnostics.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.fileproviders.abstractions\\9.0.7\\microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.fileproviders.physical\\9.0.7\\microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.filesystemglobbing\\9.0.7\\microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.hosting\\9.0.7\\microsoft.extensions.hosting.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.hosting.abstractions\\9.0.7\\microsoft.extensions.hosting.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging\\9.0.7\\microsoft.extensions.logging.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.abstractions\\9.0.7\\microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.configuration\\9.0.7\\microsoft.extensions.logging.configuration.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.console\\9.0.7\\microsoft.extensions.logging.console.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.debug\\9.0.7\\microsoft.extensions.logging.debug.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.eventlog\\9.0.7\\microsoft.extensions.logging.eventlog.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.eventsource\\9.0.7\\microsoft.extensions.logging.eventsource.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.options\\9.0.7\\microsoft.extensions.options.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.options.configurationextensions\\9.0.7\\microsoft.extensions.options.configurationextensions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.primitives\\9.0.7\\microsoft.extensions.primitives.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.playwright\\1.51.0\\microsoft.playwright.1.51.0.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog\\3.1.1\\serilog.3.1.1.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.extensions.hosting\\8.0.0\\serilog.extensions.hosting.8.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.extensions.logging\\8.0.0\\serilog.extensions.logging.8.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.sinks.console\\5.0.1\\serilog.sinks.console.5.0.1.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.diagnostics.diagnosticsource\\9.0.7\\system.diagnostics.diagnosticsource.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.diagnostics.eventlog\\9.0.7\\system.diagnostics.eventlog.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.io.pipelines\\9.0.7\\system.io.pipelines.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.text.encodings.web\\9.0.7\\system.text.encodings.web.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.text.json\\9.0.7\\system.text.json.9.0.7.nupkg.sha512"], "logs": []}