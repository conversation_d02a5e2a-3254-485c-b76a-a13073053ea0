﻿

FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Controls\PositionOrderCard.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Controls\PriceDisplayControl.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Controls\TradeStatusMonitorPanel.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Controls\TradingOperationControl.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\MainWindow.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Themes\ResponsiveLayout.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Views\BasisHistoryView.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Views\TradingConfigEditDialog.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Views\Trading\TestOverlayWindow.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Views\Trading\TradingBrowserWindow.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Views\Trading\TradingInfoOverlay.xaml;;
FH:\gold\gold\src\GoldArbitrageDesktop.WPF\Views\Trading\TradingInfoOverlayWindow.xaml;;

