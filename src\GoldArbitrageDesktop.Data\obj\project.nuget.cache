{"version": 2, "dgSpecHash": "WY5wlvxQZIg=", "success": true, "projectFilePath": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\GoldArbitrageDesktop.Data.csproj", "expectedPackageFiles": ["H:\\gold\\nuget-packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.bcl.asyncinterfaces\\9.0.7\\microsoft.bcl.asyncinterfaces.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.build.framework\\17.8.3\\microsoft.build.framework.17.8.3.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.build.locator\\1.7.8\\microsoft.build.locator.1.7.8.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.common\\4.8.0\\microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.csharp\\4.8.0\\microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.csharp.workspaces\\4.8.0\\microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.netanalyzers\\8.0.0\\microsoft.codeanalysis.netanalyzers.8.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.workspaces.common\\4.8.0\\microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.codeanalysis.workspaces.msbuild\\4.8.0\\microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.data.sqlite.core\\9.0.7\\microsoft.data.sqlite.core.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore\\9.0.7\\microsoft.entityframeworkcore.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.abstractions\\9.0.7\\microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.analyzers\\9.0.7\\microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.design\\9.0.7\\microsoft.entityframeworkcore.design.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.relational\\9.0.7\\microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.sqlite\\9.0.7\\microsoft.entityframeworkcore.sqlite.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.sqlite.core\\9.0.7\\microsoft.entityframeworkcore.sqlite.core.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.entityframeworkcore.tools\\9.0.7\\microsoft.entityframeworkcore.tools.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.caching.abstractions\\9.0.7\\microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.caching.memory\\9.0.7\\microsoft.extensions.caching.memory.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration\\9.0.7\\microsoft.extensions.configuration.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.abstractions\\9.0.7\\microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.binder\\9.0.7\\microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.commandline\\9.0.7\\microsoft.extensions.configuration.commandline.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.environmentvariables\\9.0.7\\microsoft.extensions.configuration.environmentvariables.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.fileextensions\\9.0.7\\microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.json\\9.0.7\\microsoft.extensions.configuration.json.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.configuration.usersecrets\\9.0.7\\microsoft.extensions.configuration.usersecrets.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.dependencyinjection\\9.0.7\\microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.7\\microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.dependencymodel\\9.0.7\\microsoft.extensions.dependencymodel.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics\\9.0.7\\microsoft.extensions.diagnostics.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics.abstractions\\9.0.7\\microsoft.extensions.diagnostics.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics.healthchecks\\9.0.7\\microsoft.extensions.diagnostics.healthchecks.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics.healthchecks.abstractions\\9.0.7\\microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.diagnostics.healthchecks.entityframeworkcore\\8.0.11\\microsoft.extensions.diagnostics.healthchecks.entityframeworkcore.8.0.11.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.fileproviders.abstractions\\9.0.7\\microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.fileproviders.physical\\9.0.7\\microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.filesystemglobbing\\9.0.7\\microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.hosting\\9.0.7\\microsoft.extensions.hosting.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.hosting.abstractions\\9.0.7\\microsoft.extensions.hosting.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging\\9.0.7\\microsoft.extensions.logging.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.abstractions\\9.0.7\\microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.configuration\\9.0.7\\microsoft.extensions.logging.configuration.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.console\\9.0.7\\microsoft.extensions.logging.console.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.debug\\9.0.7\\microsoft.extensions.logging.debug.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.eventlog\\9.0.7\\microsoft.extensions.logging.eventlog.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.logging.eventsource\\9.0.7\\microsoft.extensions.logging.eventsource.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.options\\9.0.7\\microsoft.extensions.options.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.options.configurationextensions\\9.0.7\\microsoft.extensions.options.configurationextensions.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.extensions.primitives\\9.0.7\\microsoft.extensions.primitives.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\mono.texttemplating\\3.0.0\\mono.texttemplating.3.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog\\3.1.1\\serilog.3.1.1.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.extensions.hosting\\8.0.0\\serilog.extensions.hosting.8.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.extensions.logging\\8.0.0\\serilog.extensions.logging.8.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.sinks.console\\5.0.1\\serilog.sinks.console.5.0.1.nupkg.sha512", "H:\\gold\\nuget-packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.10\\sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "H:\\gold\\nuget-packages\\sqlitepclraw.core\\2.1.10\\sqlitepclraw.core.2.1.10.nupkg.sha512", "H:\\gold\\nuget-packages\\sqlitepclraw.lib.e_sqlite3\\2.1.10\\sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "H:\\gold\\nuget-packages\\sqlitepclraw.provider.e_sqlite3\\2.1.10\\sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "H:\\gold\\nuget-packages\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.collections.immutable\\7.0.0\\system.collections.immutable.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.composition\\7.0.0\\system.composition.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.composition.attributedmodel\\7.0.0\\system.composition.attributedmodel.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.composition.convention\\7.0.0\\system.composition.convention.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.composition.hosting\\7.0.0\\system.composition.hosting.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.composition.runtime\\7.0.0\\system.composition.runtime.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.composition.typedparts\\7.0.0\\system.composition.typedparts.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.diagnostics.diagnosticsource\\9.0.7\\system.diagnostics.diagnosticsource.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.diagnostics.eventlog\\9.0.7\\system.diagnostics.eventlog.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.io.pipelines\\9.0.7\\system.io.pipelines.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "H:\\gold\\nuget-packages\\system.reflection.metadata\\7.0.0\\system.reflection.metadata.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\system.text.encodings.web\\9.0.7\\system.text.encodings.web.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.text.json\\9.0.7\\system.text.json.9.0.7.nupkg.sha512", "H:\\gold\\nuget-packages\\system.threading.channels\\7.0.0\\system.threading.channels.7.0.0.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.netcore.app.runtime.win-x64\\8.0.14\\microsoft.netcore.app.runtime.win-x64.8.0.14.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.windowsdesktop.app.runtime.win-x64\\8.0.14\\microsoft.windowsdesktop.app.runtime.win-x64.8.0.14.nupkg.sha512", "H:\\gold\\nuget-packages\\microsoft.aspnetcore.app.runtime.win-x64\\8.0.14\\microsoft.aspnetcore.app.runtime.win-x64.8.0.14.nupkg.sha512"], "logs": []}