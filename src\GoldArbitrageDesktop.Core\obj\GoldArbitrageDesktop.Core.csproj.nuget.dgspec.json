{"format": 1, "restore": {"H:\\gold\\gold\\src\\GoldArbitrageDesktop.Core\\GoldArbitrageDesktop.Core.csproj": {}}, "projects": {"H:\\gold\\gold\\src\\GoldArbitrageDesktop.Core\\GoldArbitrageDesktop.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Core\\GoldArbitrageDesktop.Core.csproj", "projectName": "GoldArbitrageDesktop.Core", "projectPath": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Core\\GoldArbitrageDesktop.Core.csproj", "packagesPath": "H:\\gold\\nuget-packages", "outputPath": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\Shared\\NuGetPackages"], "configFilePaths": ["H:\\gold\\gold\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://mirrors.cloud.tencent.com/nuget/": {}, "https://mirrors.huaweicloud.com/repository/nuget/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\GoldArbitrageDesktop.Data.csproj": {"projectPath": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\GoldArbitrageDesktop.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1701"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.1, )"}, "Microsoft.CodeAnalysis.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[3.3.4, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\GoldArbitrageDesktop.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\GoldArbitrageDesktop.Data.csproj", "projectName": "GoldArbitrageDesktop.Data", "projectPath": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\GoldArbitrageDesktop.Data.csproj", "packagesPath": "H:\\gold\\nuget-packages", "outputPath": "H:\\gold\\gold\\src\\GoldArbitrageDesktop.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\Shared\\NuGetPackages"], "configFilePaths": ["H:\\gold\\gold\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://mirrors.cloud.tencent.com/nuget/": {}, "https://mirrors.huaweicloud.com/repository/nuget/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1701"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[3.3.4, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Design": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}