using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Windows.Media;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Collections.ObjectModel;
using System.Windows.Shapes;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using GoldArbitrageDesktop.Services;
using GoldArbitrageDesktop.Services.Interfaces;
using GoldArbitrageDesktop.Core.Models;
using GoldArbitrageDesktop.Core.Interfaces;
using GoldArbitrageDesktop.WPF.ViewModels;
using GoldArbitrageDesktop.WPF.Services;
using GoldArbitrageDesktop.WPF.Services.Mock;
using GoldArbitrageDesktop.Core.Services;
using System.IO;
using System.Text.Json;
using System.Globalization;
using GoldArbitrageDesktop.WPF.Extensions;
using GoldArbitrageDesktop.WPF.Messages;
using CommunityToolkit.Mvvm.Messaging;
using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using System.Windows.Media.Animation;
using GoldArbitrageDesktop.WPF.DTOs;
using GoldArbitrageDesktop.Core.Models.Trading;
using GoldArbitrageDesktop.Core.Interfaces.Trading;
using GoldArbitrageDesktop.Services.Trading;
using GoldArbitrageDesktop.Services.Trading;
using GoldArbitrageDesktop.Services;

namespace GoldArbitrageDesktop.WPF
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑 - 优化版本
    /// 支持新的期货价格服务，增强UI更新和错误处理
    /// </summary>
    public partial class MainWindow : Window, IRecipient<TradeExecutionMessage>, IRecipient<NavigateToTabMessage>, IRecipient<DeletePositionOrderMessage>, IRecipient<ClosePositionOrderMessage>, INotifyPropertyChanged
    {
        /// <summary>
        /// 初始化完成事件
        /// </summary>
        public event EventHandler? InitializationCompleted;

        private SpotPriceWebSocketService? _spotPriceService;
        private IMultiPlatformSpotPriceService? _multiPlatformSpotPriceService;
        private FuturesPriceService? _futuresPriceService;
        private IOrderApiService? _orderApiService;
        private PositionOrderRealTimeService? _positionOrderRealTimeService;
        private PositionOrdersManagerViewModel? _positionOrdersManagerViewModel;
        private ICloseTaskManager? _closeTaskManager;
        private readonly Lazy<ICloseTaskManager> _lazyCloseTaskManager;

        /// <summary>
        /// 获取平仓任务管理器（延迟初始化）
        /// </summary>
        private ICloseTaskManager CloseTaskManager => _lazyCloseTaskManager.Value;

        /// <summary>
        /// 持仓订单管理ViewModel - 用于XAML数据绑定
        /// </summary>
        public PositionOrdersManagerViewModel? PositionOrdersManagerViewModel => _positionOrdersManagerViewModel;

        private readonly ILogger<MainWindow> _logger;
        private readonly IUserConfigurationService _userConfigService;
        private readonly ISpotPlatformConfigService _spotPlatformConfigService;
        private readonly IApplicationStartupService _applicationStartupService;
        private readonly ContractSelectorViewModel _contractSelectorViewModel;
        private IFuturesAccountConfigService _futuresAccountConfigService;
        private readonly IServiceProvider _serviceProvider;
        private SpotPriceData? _lastSpotPriceData;
        private FuturesPriceData? _lastFuturesPriceData;
        private string _selectedContract = "SHFE.au2508";

        // ViewModels
        public SpotPlatformMarketViewModel SpotPlatformMarket { get; private set; }
        public PriceDisplayViewModel PriceDisplayViewModel { get; private set; }
        public TradeStatusMonitorViewModel TradeStatusMonitorViewModel { get; private set; }
        public ContractSelectorViewModel ContractSelectorViewModel => _contractSelectorViewModel;

        // 🚀 期货配置属性
        private FuturesConfigViewModel _futuresConfig = new();
        public FuturesConfigViewModel FuturesConfig
        {
            get => _futuresConfig;
            set
            {
                _futuresConfig = value;
                OnPropertyChanged();
            }
        }

        // 历史订单数据
        private ObservableCollection<HistoricalOrderData> _historicalOrders = new();
        public ObservableCollection<HistoricalOrderData> HistoricalOrders
        {
            get => _historicalOrders;
            set
            {
                _historicalOrders = value;
                OnPropertyChanged();
            }
        }
        
        // 状态管理
        private bool _isSpotConnected = false;
        private bool _isFuturesConnected = false;
        private bool _isInitialized = false;
        private bool _isInitializingCheckBoxes = false; // 标志是否正在初始化CheckBox状态

        private DispatcherTimer? _uiUpdateTimer = new();
        private DispatcherTimer? _connectionCheckTimer = new();
        
        // 最后更新时间跟踪
        private DateTime _lastSpotUpdate = DateTime.MinValue;
        private DateTime _lastFuturesUpdate = DateTime.MinValue;

        /// <summary>
        /// 初始化主窗口
        /// </summary>
        public MainWindow(
            ILogger<MainWindow> logger,
            IUserConfigurationService userConfigService,
            ISpotPlatformConfigService spotPlatformConfigService,
            IApplicationStartupService applicationStartupService,
            ContractSelectorViewModel contractSelectorViewModel,
            IFuturesAccountConfigService futuresAccountConfigService,
            IServiceProvider serviceProvider)
        {
            try
            {
                _logger = logger ?? throw new ArgumentNullException(nameof(logger));
                _logger.LogInformation("🔧 MainWindow构造函数开始...");

                InitializeComponent();
                _logger.LogInformation("🔧 InitializeComponent完成");

                // 启动加载动画
                StartLoadingAnimation();

                // 通过构造函数注入获取服务，避免在构造函数中访问Application.Current
                _userConfigService = userConfigService ?? throw new ArgumentNullException(nameof(userConfigService));
                _spotPlatformConfigService = spotPlatformConfigService ?? throw new ArgumentNullException(nameof(spotPlatformConfigService));
                _applicationStartupService = applicationStartupService ?? throw new ArgumentNullException(nameof(applicationStartupService));
                _logger.LogInformation("🔧 开始验证contractSelectorViewModel参数...");
                _contractSelectorViewModel = contractSelectorViewModel ?? throw new ArgumentNullException(nameof(contractSelectorViewModel));
                _futuresAccountConfigService = futuresAccountConfigService ?? throw new ArgumentNullException(nameof(futuresAccountConfigService));
                _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

                // 初始化延迟加载的服务
                _lazyCloseTaskManager = new Lazy<ICloseTaskManager>(() => _serviceProvider.GetRequiredService<ICloseTaskManager>());

                _logger.LogInformation("🔧 所有依赖注入参数验证完成");

                // 延迟初始化其他服务，避免在构造函数中访问Application.Current
                // 这些服务将在MainWindow_Loaded事件中初始化
                _logger.LogInformation("🔧 准备注册Loaded事件...");

                // 在窗口加载完成后进行完整初始化
                this.Loaded += MainWindow_Loaded;
                _logger.LogInformation("🔧 Loaded事件注册完成");

                _logger.LogInformation("✅ 主窗口构造函数完成，等待窗口加载事件进行完整初始化");
            }
            catch (Exception ex)
            {
                // 如果logger还没有初始化，使用Console输出
                if (_logger != null)
                {
                    _logger.LogError(ex, "❌ MainWindow构造函数发生异常");
                }
                else
                {
                    Console.WriteLine($"❌ MainWindow构造函数发生异常: {ex}");
                }
                throw;
            }
        }

        /// <summary>
        /// 订阅所有事件
        /// </summary>
        private void SubscribeToEvents()
        {
            try
            {
                // 订阅单平台现货价格服务事件
                if (_spotPriceService != null)
                    _spotPriceService.PriceUpdated += OnSpotPriceUpdated;

                // 订阅多平台现货价格服务事件
                if (_multiPlatformSpotPriceService != null)
                {
                    _multiPlatformSpotPriceService.PriceUpdated += OnMultiPlatformSpotPriceUpdated;
                    _multiPlatformSpotPriceService.ConnectionStatusChanged += OnMultiPlatformConnectionStatusChanged;
                    _logger.LogInformation("已订阅多平台现货价格服务事件");
                }

                // 订阅期货价格服务事件
                if (_futuresPriceService != null)
                {
                    _futuresPriceService.PriceUpdated += OnFuturesPriceUpdated;
                    _futuresPriceService.MarketStatusChanged += OnMarketStatusChanged;
                }

                _contractSelectorViewModel.ContractChanged += OnContractChanged;

                _logger.LogInformation("已订阅所有价格更新事件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "订阅事件时发生错误");
            }
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("🚀 窗口已加载，开始完整初始化流程...");

                // 初始化其他服务（在窗口加载后安全地访问Application.Current）
                await InitializeServicesAfterWindowLoaded();

                // 添加窗口关闭事件监听
                this.Closing += MainWindow_Closing;
                this.Closed += MainWindow_Closed;

                // 添加全局异常处理
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;

                // 显示初始化状态
                UpdateConnectionStatus("正在初始化服务...");

                // 启动应用程序服务（包括API服务器）
                try
                {
                    _logger.LogInformation("🚀 开始启动应用程序服务...");
                    UpdateLoadingStatus("正在启动API服务器...");
                    await Task.Delay(500); // 让用户看到状态

                    await _applicationStartupService.StartAsync();
                    _logger.LogInformation("✅ 应用程序服务启动完成");

                    UpdateConnectionStatus("API服务器已启动");
                    UpdateLoadingStatus("正在连接数据服务...");
                    await Task.Delay(300); // 让用户看到状态更新
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 启动应用程序服务失败");
                    UpdateConnectionStatus("服务启动失败");
                    // 继续执行，不阻止应用程序启动
                }

                // 🔧 直接初始化ViewModels，不使用重试机制
                _ = Task.Run(async () =>
                {
                    await InitializeViewModelsAsync();
                });

                // 初始化用户配置
                try
                {
                    _logger.LogInformation("📋 开始初始化用户配置...");
                    UpdateLoadingStatus("正在初始化用户配置...");
                    await InitializeUserConfigurationAsync();
                    _logger.LogInformation("✅ 用户配置初始化成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 用户配置初始化失败，但应用程序将继续运行");
                    UpdateConnectionStatus("用户配置初始化失败");
                }

                // 启动服务连接
                try
                {
                    _logger.LogInformation("🔧 开始初始化服务...");
                    UpdateLoadingStatus("正在连接数据服务...");
                    await InitializeServicesAsync();
                    _logger.LogInformation("✅ 服务初始化成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 服务初始化失败，但应用程序将继续运行");
                    UpdateConnectionStatus("服务初始化失败");
                }

                // 等待一段时间让服务稳定
                _logger.LogInformation("⏳ 等待服务稳定...");
                await Task.Delay(2000);

                // 等待ViewModels初始化完成
                _logger.LogInformation("⏳ 等待ViewModels初始化完成...");
                UpdateLoadingStatus("等待ViewModels初始化...");
                await WaitForViewModelsInitializationAsync();

                // 尝试获取初始数据
                try
                {
                    _logger.LogInformation("📊 开始加载初始数据...");
                    UpdateLoadingStatus("正在加载市场数据...");
                    await LoadInitialDataAsync();
                    _logger.LogInformation("✅ 初始数据加载成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 初始数据加载失败，但应用程序将继续运行");
                    UpdateConnectionStatus("数据加载失败");
                }

                // 启动持仓订单实时更新服务
                try
                {
                    if (_positionOrderRealTimeService != null)
                    {
                        _logger.LogInformation("🔄 开始启动持仓订单实时更新服务...");
                        UpdateLoadingStatus("正在启动实时更新服务...");
                        await _positionOrderRealTimeService.StartAsync();
                        _logger.LogInformation("✅ 持仓订单实时更新服务启动成功");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 持仓订单实时更新服务启动失败，但应用程序将继续运行");
                }

                _isInitialized = true;
                _logger.LogInformation("🎉 主窗口初始化完成，应用程序已准备就绪");
                UpdateConnectionStatus("应用程序已就绪");
                UpdateLoadingStatus("启动完成！");

                // 🚀 延迟一小段时间，让用户看到"启动完成"状态
                await Task.Delay(800);

                // 隐藏启动画面
                try
                {
                    _logger.LogInformation("🔧 开始隐藏启动画面...");
                    UpdateLoadingStatus("正在进入主界面...");
                    await Task.Delay(200); // 让用户看到状态更新

                    HideSplashScreen();
                    _logger.LogInformation("✅ 启动画面已隐藏");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 隐藏启动画面失败");
                    // 如果动画失败，强制隐藏
                    try
                    {
                        SplashScreenOverlay.Visibility = Visibility.Collapsed;
                    }
                    catch { }
                }

                // 确保窗口完全激活和聚焦
                this.Show();
                this.Activate();
                this.Focus();
                this.Topmost = true;  // 临时置顶
                await Task.Delay(100);
                this.Topmost = false; // 取消置顶

                _logger.LogInformation("🖥️ 窗口已强制显示并激活");

                // 触发初始化完成事件，通知App.xaml.cs
                try
                {
                    _logger.LogInformation("🔔 触发初始化完成事件...");
                    InitializationCompleted?.Invoke(this, EventArgs.Empty);
                    _logger.LogInformation("✅ 初始化完成事件已触发");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 触发初始化完成事件失败");
                }
            }
            catch (Exception ex)
            {
                try
                {
                    _logger?.LogError(ex, "❌ 主窗口初始化过程中发生严重错误");
                    UpdateConnectionStatus("初始化失败");

                    // 即使初始化失败，也要确保应用程序继续运行
                    _isInitialized = true;
                    _logger?.LogInformation("⚠️ 尽管初始化失败，应用程序仍将继续运行");

                    // 强制保持窗口显示
                    this.Show();
                    this.Activate();
                }
                catch (Exception innerEx)
                {
                    // 最后的安全网
                    System.Console.WriteLine($"严重错误: {innerEx.Message}");
                    System.Console.WriteLine($"原始错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 在窗口加载后初始化其他服务
        /// </summary>
        private async Task InitializeServicesAfterWindowLoaded()
        {
            try
            {
                _logger.LogInformation("🔧 开始初始化其他服务...");

                // 现在可以安全地访问Application.Current
                var app = (App)Application.Current;
                var serviceProvider = app.ServiceProvider;

                _logger.LogInformation("🔧 [DEBUG] 步骤1: 获取serviceProvider完成");

                // 检查是否启用Mock模式
                var mockConfig = serviceProvider.GetService<MockConfiguration>();
                if (mockConfig?.IsMockEnabled == true)
                {
                    _logger.LogInformation("🎭 检测到Mock模式已启用，将使用Mock服务适配器");
                    // 在Mock模式下，我们不直接使用真实服务，而是通过MockServiceAdapter
                    // 这些服务引用主要用于兼容性，实际数据通过Mock事件提供
                    _spotPriceService = serviceProvider.GetService<SpotPriceWebSocketService>();
                    _futuresPriceService = serviceProvider.GetService<FuturesPriceService>();

                    // 直接订阅Mock服务适配器的事件
                    var mockServiceAdapter = serviceProvider.GetService<MockServiceAdapter>();
                    if (mockServiceAdapter != null)
                    {
                        mockServiceAdapter.SpotPriceUpdated += OnMockSpotPriceUpdated;
                        mockServiceAdapter.FuturesPriceUpdated += OnMockFuturesPriceUpdated;
                        _logger.LogInformation("✅ 已直接订阅Mock服务适配器事件");
                    }

                    // 订阅Mock数据服务的现货平台数据更新事件
                    var mockDataService = serviceProvider.GetService<IMockDataService>();
                    if (mockDataService != null)
                    {
                        mockDataService.MockSpotPlatformsUpdated += OnMockSpotPlatformsUpdated;
                        mockDataService.MockPositionOrdersUpdated += OnMockPositionOrdersUpdated;
                        _logger.LogInformation("✅ 已订阅Mock现货平台数据和持仓订单更新事件");
                    }
                }
                else
                {
                    _logger.LogInformation("📊 使用真实数据服务");
                    _spotPriceService = serviceProvider.GetRequiredService<SpotPriceWebSocketService>();
                    _multiPlatformSpotPriceService = serviceProvider.GetRequiredService<IMultiPlatformSpotPriceService>();
                    _futuresPriceService = serviceProvider.GetRequiredService<FuturesPriceService>();
                    _orderApiService = serviceProvider.GetRequiredService<IOrderApiService>();

                    // 初始化持仓订单实时更新服务
                    _logger.LogInformation("🔧 [DEBUG] 步骤2.1: 开始获取PositionOrderRealTimeService...");
                    _positionOrderRealTimeService = serviceProvider.GetRequiredService<PositionOrderRealTimeService>();
                    _logger.LogInformation("🔧 [DEBUG] 步骤2.2: PositionOrderRealTimeService获取完成");

                    _logger.LogInformation("🔧 [DEBUG] 步骤2.3: 开始获取PositionOrdersManagerViewModel...");
                    _positionOrdersManagerViewModel = serviceProvider.GetRequiredService<PositionOrdersManagerViewModel>();
                    _logger.LogInformation("🔧 [DEBUG] 步骤2.4: PositionOrdersManagerViewModel获取完成");

                    _logger.LogInformation("🔧 [DEBUG] 步骤2.5: 开始设置PositionOrdersManager...");
                    _positionOrderRealTimeService.PositionOrdersManager = _positionOrdersManagerViewModel;
                    _logger.LogInformation("🔧 [DEBUG] 步骤2.6: PositionOrdersManager设置完成");

                    // 初始化平仓任务管理器 - 延迟初始化以避免循环依赖
                    _logger.LogInformation("🔧 [DEBUG] 步骤2.7: ICloseTaskManager将延迟初始化");
                    _logger.LogInformation("🔧 [DEBUG] 步骤2.8: ICloseTaskManager延迟初始化设置完成");

                    // 注意：订单数据加载将在API服务器启动完成后的LoadInitialDataAsync中执行
                }

                _logger.LogInformation("🔧 [DEBUG] 步骤3: 开始初始化合约选择器ViewModel...");

                // 🔧 修复死锁：不等待合约选择器初始化完成，避免UI线程阻塞
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _contractSelectorViewModel.InitializeAsync();
                        _logger.LogInformation("🔧 [DEBUG] 步骤3.1: 合约选择器ViewModel后台初始化完成");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "合约选择器ViewModel后台初始化失败");
                    }
                });
                _logger.LogInformation("🔧 [DEBUG] 步骤3.1: 合约选择器ViewModel后台初始化已启动");

                // 确保MainWindow和ViewModel的选中合约保持同步
                _selectedContract = _contractSelectorViewModel.SelectedContract;
                _logger.LogInformation("🔧 [DEBUG] 步骤3.2: 合约同步完成");

                // 订阅合约变更事件
                _contractSelectorViewModel.ContractChanged += OnContractSelectorChanged;
                _logger.LogInformation("🔧 [DEBUG] 步骤3.3: 合约变更事件订阅完成");

                // 订阅价格更新事件
                SubscribeToEvents();
                _logger.LogInformation("🔧 [DEBUG] 步骤3.4: 价格更新事件订阅完成");

                // 初始化合约选择器UI
                InitializeContractSelector();
                _logger.LogInformation("🔧 [DEBUG] 步骤3.5: 合约选择器UI初始化完成");

                // 注册消息接收
                WeakReferenceMessenger.Default.Register<TradeExecutionMessage>(this);
                WeakReferenceMessenger.Default.Register<NavigateToTabMessage>(this);
                WeakReferenceMessenger.Default.Register<DeletePositionOrderMessage>(this);
                WeakReferenceMessenger.Default.Register<ClosePositionOrderMessage>(this);

                _logger.LogInformation("✅ 其他服务初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 初始化其他服务时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 窗口正在关闭事件处理
        /// </summary>
        private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            _logger.LogWarning("⚠️ 窗口正在关闭，原因: 用户操作或程序逻辑");

            // 这里可以添加确认对话框
            // var result = MessageBox.Show("确定要退出应用程序吗？", "确认退出", MessageBoxButton.YesNo, MessageBoxImage.Question);
            // if (result == MessageBoxResult.No)
            // {
            //     e.Cancel = true;
            //     return;
            // }
        }

        /// <summary>
        /// 窗口已关闭事件处理
        /// </summary>
        private void MainWindow_Closed(object? sender, EventArgs e)
        {
            _logger.LogWarning("❌ 窗口已关闭，应用程序即将退出");
        }

        /// <summary>
        /// 处理未处理的异常
        /// </summary>
        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                _logger?.LogError(exception, "🚨 发生未处理的异常，IsTerminating: {IsTerminating}", e.IsTerminating);

                if (!e.IsTerminating)
                {
                    _logger?.LogInformation("⚠️ 应用程序将尝试继续运行");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"异常处理器本身发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理UI线程未处理的异常
        /// </summary>
        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                _logger?.LogError(e.Exception, "🚨 UI线程发生未处理的异常");

                // 标记异常已处理，防止应用程序崩溃
                e.Handled = true;

                _logger?.LogInformation("✅ 异常已处理，应用程序将继续运行");

                // 更新UI状态
                UpdateConnectionStatus("发生错误，但应用程序正常运行");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"UI异常处理器本身发生错误: {ex.Message}");
                e.Handled = true; // 即使处理器出错，也要防止崩溃
            }
        }

        /// <summary>
        /// 初始化用户配置
        /// </summary>
        private async Task InitializeUserConfigurationAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化用户配置...");

                // 初始化用户默认配置（如果不存在）
                await _userConfigService.InitializeUserDefaultConfigAsync(1);

                // 加载用户配置并设置CheckBox状态
                var userConfig = await _userConfigService.GetUserConfigAsync(1);

                // 设置CheckBox的初始状态
                await SetPlatformCheckBoxStates(userConfig);

                // 🚀 加载期货配置
                await LoadFuturesConfigAsync();

                _logger.LogInformation("用户配置初始化完成，启用平台数: {EnabledCount}",
                    userConfig.GetEnabledPlatformCount());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户配置时发生错误");
            }
        }

        /// <summary>
        /// 根据用户配置设置平台CheckBox状态
        /// </summary>
        /// <param name="userConfig">用户配置</param>
        private async Task SetPlatformCheckBoxStates(UserSpotPlatformConfig userConfig)
        {
            try
            {
                // 直接映射平台ID到复选框控件名称
                var platformCheckBoxMap = new Dictionary<string, string>
                {
                    { "chuangfu", "ChuangfuCheckbox" },
                    { "rencheng", "RenchengCheckbox" },
                    { "fupai", "FupaiCheckbox" },
                    { "yinghuida", "YinghuidaCheckbox" }
                };

                // 暂时禁用事件处理，避免在初始化时触发配置变更事件
                _isInitializingCheckBoxes = true;

                foreach (var kvp in platformCheckBoxMap)
                {
                    string platformId = kvp.Key;
                    string checkBoxName = kvp.Value;

                    // 查找CheckBox控件
                    var checkBox = FindName(checkBoxName) as CheckBox;
                    if (checkBox != null)
                    {
                        // 设置CheckBox状态（不会触发事件，因为_isInitializingCheckBoxes为true）
                        bool isEnabled = userConfig.IsPlatformEnabled(platformId);
                        checkBox.IsChecked = isEnabled;

                        _logger.LogDebug("设置平台 {PlatformId} CheckBox状态为: {IsEnabled}",
                            platformId, isEnabled);

                        // 同时更新状态显示
                        await UpdatePlatformStatusDisplay(platformId, isEnabled);
                    }
                    else
                    {
                        _logger.LogWarning("未找到平台 {PlatformId} 对应的CheckBox控件: {CheckBoxName}",
                            platformId, checkBoxName);
                    }
                }

                // 重新启用事件处理
                _isInitializingCheckBoxes = false;

                _logger.LogInformation("已设置所有平台CheckBox状态，启用平台数: {EnabledCount}",
                    userConfig.GetEnabledPlatformCount());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置平台CheckBox状态时发生错误");
                // 确保在异常情况下也重新启用事件处理
                _isInitializingCheckBoxes = false;
            }
        }

        /// <summary>
        /// 初始化所有服务
        /// </summary>
        private async Task InitializeServicesAsync()
        {
            _logger.LogInformation("开始初始化所有服务...");
            
            // 并行启动现货和期货服务
            var spotTask = InitializeSpotServiceAsync();
            var futuresTask = InitializeFuturesServiceAsync();
            
            try
            {
                await Task.WhenAll(spotTask, futuresTask);
                _logger.LogInformation("所有服务初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "服务初始化过程中发生错误");
            }
        }

        /// <summary>
        /// 初始化现货服务
        /// </summary>
        private async Task InitializeSpotServiceAsync()
        {
            try
            {
                // 优先使用多平台服务
                if (_multiPlatformSpotPriceService != null)
                {
                    _logger.LogInformation("正在初始化多平台现货WebSocket服务...");

                    // 获取用户启用的平台配置
                    var userConfig = await _userConfigService.GetUserConfigAsync(1); // 默认用户ID为1
                    var enabledPlatforms = userConfig.EnabledPlatforms
                        .Where(kvp => kvp.Value)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    _logger.LogInformation("用户启用的平台: {EnabledPlatforms}", string.Join(", ", enabledPlatforms));

                    // 根据用户配置启动平台
                    if (enabledPlatforms.Any())
                    {
                        await _multiPlatformSpotPriceService.StartEnabledPlatformsAsync(enabledPlatforms);
                    }
                    else
                    {
                        _logger.LogWarning("用户没有启用任何现货平台");
                    }

                    _isSpotConnected = true;
                    _logger.LogInformation("多平台现货WebSocket服务连接成功");
                }
                else if (_spotPriceService != null)
                {
                    _logger.LogInformation("正在初始化单平台现货WebSocket服务...");
                    await _spotPriceService.ConnectAsync();
                    _isSpotConnected = true;
                    _logger.LogInformation("单平台现货WebSocket服务连接成功");
                }
                else
                {
                    _logger.LogWarning("现货价格服务未初始化（可能在Mock模式下）");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "现货WebSocket服务连接失败");
                _isSpotConnected = false;
            }
        }

        /// <summary>
        /// 初始化期货服务
        /// </summary>
        private async Task InitializeFuturesServiceAsync()
        {
            try
            {
                // 🚀 1. 先初始化期货价格服务（启动Python脚本和HTTP服务器）
                if (_futuresPriceService != null)
                {
                    _logger.LogInformation("🔧 正在初始化期货价格服务（优先启动Python服务）...");

                    // 启动期货价格服务（这将创建Python脚本并启动天勤SDK）
                    await _futuresPriceService.StartAsync();

                    // 等待一段时间让Python HTTP服务器完全启动
                    _logger.LogInformation("⏳ 等待Python HTTP服务器启动...");
                    await Task.Delay(8000); // 增加等待时间确保HTTP服务器启动

                    // 检查服务状态
                    var isMarketOpen = _futuresPriceService.IsMarketOpen;
                    _logger.LogInformation("期货价格服务启动完成，市场状态: {Status}", isMarketOpen ? "开市" : "休市");

                    // 尝试获取初始数据
                    var initialPrice = _futuresPriceService.GetLatestPrice();
                    if (initialPrice != null)
                    {
                        _logger.LogInformation("获取到初始期货价格数据: {Data}", initialPrice.ToDisplayString());
                        _isFuturesConnected = true;
                    }
                    else
                    {
                        _logger.LogInformation("等待期货价格数据...");
                        // 期货服务已启动，连接状态设为true，数据会通过事件更新
                        _isFuturesConnected = true;
                    }
                }
                else
                {
                    _logger.LogWarning("期货价格服务未初始化（可能在Mock模式下）");
                }

                // 🚀 2. 然后初始化期货交易服务（此时Python HTTP服务器应该已经启动）
                try
                {
                    _logger.LogInformation("🔧 正在初始化期货交易服务...");
                    var futuresTradingService = _serviceProvider.GetService<GoldArbitrageDesktop.Core.Interfaces.Trading.IFuturesTradingService>();
                    if (futuresTradingService != null)
                    {
                        const int defaultUserId = 1; // 使用默认用户ID

                        // 🚀 修复：增加重试机制
                        const int maxRetries = 3;
                        bool initSuccess = false;

                        for (int attempt = 1; attempt <= maxRetries; attempt++)
                        {
                            _logger.LogInformation("🔄 期货交易服务初始化尝试 {Attempt}/{MaxRetries}", attempt, maxRetries);

                            var tradingInitSuccess = await futuresTradingService.InitializeFastTradingAsync(defaultUserId);
                            if (tradingInitSuccess)
                            {
                                _logger.LogInformation("✅ 期货交易服务初始化成功: 尝试次数={Attempt}", attempt);
                                initSuccess = true;
                                break;
                            }

                            if (attempt < maxRetries)
                            {
                                _logger.LogWarning("⚠️ 期货交易服务初始化失败，等待重试: 尝试次数={Attempt}", attempt);
                                await Task.Delay(3000 * attempt); // 递增延迟
                            }
                        }

                        if (!initSuccess)
                        {
                            _logger.LogError("❌ 期货交易服务初始化失败，已达到最大重试次数: {MaxRetries}", maxRetries);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("⚠️ 期货交易服务未注册");
                    }
                }
                catch (Exception tradingEx)
                {
                    _logger.LogError(tradingEx, "❌ 期货交易服务初始化异常，但应用程序将继续运行");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "期货服务初始化失败");
                _isFuturesConnected = false;
            }
        }

        /// <summary>
        /// 加载初始数据
        /// </summary>
        private async Task LoadInitialDataAsync()
        {
            try
            {
                _logger.LogInformation("正在加载初始数据，当前选中合约: {Contract}", _selectedContract);

                // 初始化现货平台数据
                if (SpotPlatformMarket != null)
                {
                    _logger.LogInformation("正在初始化现货平台数据...");
                    await SpotPlatformMarket.LoadPlatformDataAsync();
                    _logger.LogInformation("现货平台数据初始化完成");
                }

                // 检查期货服务状态
                var isMarketOpen = _futuresPriceService?.IsMarketOpen ?? false;
                _logger.LogInformation("期货市场状态: {Status}", isMarketOpen ? "开市" : "休市");

                // 尝试获取期货价格数据
                var latestPrice = _futuresPriceService?.GetLatestPrice();
                if (latestPrice != null)
                {
                    _logger.LogInformation("获取到初始期货价格数据: {Data}", latestPrice.ToDisplayString());
                    OnFuturesPriceUpdated(this, latestPrice);
                    _isFuturesConnected = true;
                }
                else
                {
                    _logger.LogWarning("未能获取初始期货价格数据，等待实时更新...");
                    
                    // 等待最多10秒获取数据
                    for (int i = 0; i < 10; i++)
                    {
                        await Task.Delay(1000);
                        latestPrice = _futuresPriceService?.GetLatestPrice();
                        if (latestPrice != null)
                        {
                            _logger.LogInformation("延迟获取到期货价格数据: {Data}", latestPrice.ToDisplayString());
                            OnFuturesPriceUpdated(this, latestPrice);
                            _isFuturesConnected = true;
                            break;
                        }
                    }
                }

                // 更新连接状态显示
                UpdateConnectionStatus();

                // 加载现货平台数据
                try
                {
                    _logger.LogInformation("🏪 开始加载现货平台数据...");
                    UpdateLoadingStatus("正在加载现货平台数据...");
                    await Task.Delay(200);

                    await SpotPlatformMarket.LoadPlatformDataAsync();
                    _logger.LogInformation("✅ 现货平台数据加载成功");
                    UpdateLoadingStatus("现货平台数据已加载");
                    await Task.Delay(200);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 现货平台数据加载失败");
                    UpdateLoadingStatus("现货平台数据加载失败，继续启动...");
                    await Task.Delay(300);
                }

                // 加载Mock数据（如果启用了Mock模式）
                UpdateLoadingStatus("正在检查Mock配置...");
                await Task.Delay(200);
                await LoadMockDataAsync();

                // 初始化ViewModels（确保在API服务器启动后执行）
                try
                {
                    _logger.LogInformation("🎨 开始初始化ViewModels...");
                    UpdateLoadingStatus("正在初始化界面组件...");
                    await Task.Delay(300);

                    // 初始化PriceDisplayViewModel
                    if (PriceDisplayViewModel != null)
                    {
                        UpdateLoadingStatus("正在初始化价格显示组件...");
                        await Task.Delay(200);
                        await PriceDisplayViewModel.InitializeAsync();
                        _logger.LogInformation("✅ PriceDisplayViewModel初始化成功");
                    }

                    UpdateLoadingStatus("界面组件初始化完成");
                    await Task.Delay(200);
                    _logger.LogInformation("✅ ViewModels初始化完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ ViewModels初始化失败");
                    UpdateLoadingStatus("界面组件初始化失败，继续启动...");
                    await Task.Delay(300);
                }

                // 加载真实订单数据（如果未启用Mock模式）
                var app = (App)Application.Current;
                var serviceProvider = app.ServiceProvider;
                var mockConfig = serviceProvider.GetService<MockConfiguration>();

                if (mockConfig?.IsMockEnabled != true && _orderApiService != null)
                {
                    try
                    {
                        _logger.LogInformation("📋 开始加载真实订单数据...");
                        await LoadRealOrderDataAsync();
                        _logger.LogInformation("✅ 真实订单数据加载成功");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ 真实订单数据加载失败");
                    }
                }

                // 初始化平台计数显示
                await UpdatePlatformCount();

                // 初始化交易配置显示
                try
                {
                    _logger.LogInformation("📋 开始初始化交易配置显示...");
                    RefreshTradingConfigDisplay();
                    _logger.LogInformation("✅ 交易配置显示初始化成功");
                }
                catch (Exception configEx)
                {
                    _logger.LogError(configEx, "❌ 交易配置显示初始化失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载初始数据失败");
            }
        }

        /// <summary>
        /// 加载Mock数据
        /// </summary>
        private async Task LoadMockDataAsync()
        {
            try
            {
                var app = (App)Application.Current;
                var serviceProvider = app.ServiceProvider;
                var mockConfig = serviceProvider.GetService<MockConfiguration>();

                if (mockConfig?.IsMockEnabled == true)
                {
                    var mockDataService = serviceProvider.GetService<IMockDataService>();
                    if (mockDataService != null)
                    {
                        _logger.LogInformation("🎭 开始加载Mock数据...");

                        // 加载订单统计汇总数据
                        var orderStats = await mockDataService.GetMockOrderStatisticsSummaryAsync();
                        UpdateOrderStatisticsDisplay(orderStats);

                        // 加载历史订单数据
                        var historicalOrders = await mockDataService.GetMockHistoricalOrdersAsync();
                        UpdateHistoricalOrdersDisplay(historicalOrders);

                        _logger.LogInformation("✅ Mock数据加载完成");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载Mock数据失败");
            }
        }



        /// <summary>
        /// Mock现货价格更新事件处理
        /// </summary>
        private void OnMockSpotPriceUpdated(object? sender, SpotPriceData priceData)
        {
            // 在UI线程更新价格显示
            Dispatcher.Invoke(() =>
            {
                try
                {
                    _logger.LogInformation($"🎭 收到Mock现货价格: 买入={priceData.BuyPrice:F2}, 卖出={priceData.SellPrice:F2}");
                    UpdateSpotPriceDisplay(priceData);
                    UpdateConnectionStatus();

                    if (_isInitialized)
                    {
                        UpdateArbitrageInfo();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新Mock现货价格显示时发生错误");
                }
            });
        }

        /// <summary>
        /// Mock期货价格更新事件处理
        /// </summary>
        private void OnMockFuturesPriceUpdated(object? sender, FuturesPriceData priceData)
        {
            // 在UI线程更新价格显示
            Dispatcher.Invoke(() =>
            {
                try
                {
                    _logger.LogInformation($"🎭 收到Mock期货价格: 买入={priceData.BuyPrice:F2}, 卖出={priceData.SellPrice:F2}");
                    UpdateFuturesPriceDisplay(priceData);
                    UpdateConnectionStatus();

                    if (_isInitialized)
                    {
                        UpdateArbitrageInfo();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新Mock期货价格显示时发生错误");
                }
            });
        }

        /// <summary>
        /// Mock现货平台数据更新事件处理
        /// </summary>
        private void OnMockSpotPlatformsUpdated(object? sender, SpotPlatformData[] platformsData)
        {
            // 在UI线程更新现货平台显示
            Dispatcher.Invoke(() =>
            {
                try
                {
                    _logger.LogInformation($"🎭 收到Mock现货平台数据: {platformsData.Length}个平台");
                    UpdateSpotPlatformsDisplay(platformsData);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新Mock现货平台数据显示时发生错误");
                }
            });
        }

        /// <summary>
        /// Mock持仓订单数据更新事件处理
        /// </summary>
        private async void OnMockPositionOrdersUpdated(object? sender, PositionOrderData[] positionOrders)
        {
            // 在UI线程更新持仓订单显示
            await Dispatcher.InvokeAsync(async () =>
            {
                try
                {
                    _logger.LogInformation($"🎭 收到Mock持仓订单数据: {positionOrders.Length}个订单");
                    await UpdatePositionOrdersDisplay(positionOrders);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新Mock持仓订单数据显示时发生错误");
                }
            });
        }

        /// <summary>
        /// 加载真实订单数据
        /// </summary>
        private async Task LoadRealOrderDataAsync()
        {
            if (_orderApiService == null)
            {
                _logger.LogWarning("订单API服务未初始化");
                return;
            }

            try
            {
                _logger.LogInformation("📡 开始加载真实订单数据...");

                // 加载持仓订单
                var positionResponse = await _orderApiService.GetPositionOrdersAsync();
                if (positionResponse.Success && positionResponse.Data != null)
                {
                    _logger.LogInformation($"✅ 成功加载 {positionResponse.Data.Count} 个持仓订单");

                    // 在UI线程更新显示
                    await Dispatcher.InvokeAsync(async () =>
                    {
                        await UpdatePositionOrdersDisplay(positionResponse.Data.PositionOrders);
                    });
                }
                else
                {
                    _logger.LogWarning($"加载持仓订单失败: {positionResponse.Message}");
                }

                // 加载历史订单
                var historyResponse = await _orderApiService.GetHistoryOrdersAsync(1, 1, 20);
                if (historyResponse.Success && historyResponse.Data != null)
                {
                    _logger.LogInformation($"✅ 成功加载 {historyResponse.Data.Orders.Length} 个历史订单");

                    // 在UI线程更新显示
                    Dispatcher.Invoke(() =>
                    {
                        UpdateHistoricalOrdersDisplay(historyResponse.Data.Orders);
                    });
                }
                else
                {
                    _logger.LogWarning($"加载历史订单失败: {historyResponse.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载真实订单数据时发生异常");
            }
        }

        private void OnSpotPriceUpdated(object? sender, SpotPriceData priceData)
        {
            try
            {
                // 检查应用程序状态
                if (Application.Current == null || Dispatcher == null || Dispatcher.HasShutdownStarted)
                {
                    return;
                }

                // 在UI线程更新价格显示
                Dispatcher.Invoke(() =>
                {
                    try
                {
                    UpdateSpotPriceDisplay(priceData);
                    UpdateConnectionStatus();

                    if (_isInitialized)
                    {
                        UpdateArbitrageInfo();
                    }

                    // 发送现货价格更新消息给持仓订单管理器
                    var messenger = ((App)Application.Current).ServiceProvider?.GetService<IMessenger>();
                    if (messenger != null)
                    {
                        var spotPlatformData = new SpotPlatformDataResponse
                        {
                            PlatformId = priceData.PlatformId,
                            PlatformName = priceData.PlatformName,
                            BuyPrice = priceData.BuyPrice,
                            SellPrice = priceData.SellPrice,
                            UpdateTime = priceData.UpdateTime,
                            IsOnline = true
                        };
                        var spotPriceMessage = new SpotPriceUpdateMessage(spotPlatformData);
                        messenger.Send(spotPriceMessage);
                        _logger.LogInformation("✅ 已发送现货价格更新消息: 平台={PlatformId}, 买入={BuyPrice:F2}, 卖出={SellPrice:F2}",
                            priceData.PlatformId, priceData.BuyPrice, priceData.SellPrice);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新现货价格显示时发生错误");
                }
            });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理现货价格更新消息时发生错误");
            }
        }

        /// <summary>
        /// 处理多平台现货价格更新事件
        /// </summary>
        private void OnMultiPlatformSpotPriceUpdated(object? sender, SpotPriceData priceData)
        {
            try
            {
                // 检查应用程序状态
                if (Application.Current == null || Dispatcher == null || Dispatcher.HasShutdownStarted)
                {
                    return;
                }

                // 在UI线程更新价格显示
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        _logger.LogDebug("收到多平台现货价格更新: {Platform} - 买入:{BuyPrice}, 卖出:{SellPrice}",
                            priceData.PlatformName, priceData.BuyPrice, priceData.SellPrice);

                        // 更新价格显示（复用现有方法）
                        UpdateSpotPriceDisplay(priceData);
                        UpdateConnectionStatus();

                        if (_isInitialized)
                        {
                            UpdateArbitrageInfo();
                        }

                        // 发送现货价格更新消息给持仓订单管理器
                        var app = Application.Current as App;
                        var messenger = app?.ServiceProvider?.GetService<IMessenger>();
                        if (messenger != null)
                    {
                        var spotPlatformData = new SpotPlatformDataResponse
                        {
                            PlatformId = priceData.PlatformId,
                            PlatformName = priceData.PlatformName,
                            BuyPrice = priceData.BuyPrice,
                            SellPrice = priceData.SellPrice,
                            UpdateTime = priceData.UpdateTime,
                            IsOnline = true
                        };
                        var spotPriceMessage = new SpotPriceUpdateMessage(spotPlatformData);
                        messenger.Send(spotPriceMessage);
                        _logger.LogInformation("✅ 已发送多平台现货价格更新消息: 平台={PlatformId}, 买入={BuyPrice:F2}, 卖出={SellPrice:F2}",
                            priceData.PlatformId, priceData.BuyPrice, priceData.SellPrice);
                    }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "更新多平台现货价格显示时发生错误: {Platform}", priceData.PlatformName);
                    }
                }), DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 处理平台 {Platform} 价格更新事件时发生异常", priceData?.PlatformName ?? "未知");
            }
        }

        /// <summary>
        /// 处理多平台连接状态变化事件
        /// </summary>
        private void OnMultiPlatformConnectionStatusChanged(object? sender, PlatformConnectionStatusEventArgs args)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    _logger.LogInformation("平台 {Platform} 连接状态变化: {Status}",
                        args.PlatformName, args.IsConnected ? "已连接" : "已断开");

                    // 更新连接状态显示
                    UpdateConnectionStatus();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理多平台连接状态变化时发生错误: {Platform}", args.PlatformName);
                }
            });
        }

        /// <summary>
        /// 更新连接状态显示
        /// </summary>
        private void UpdateConnectionStatus(string? customMessage = null)
        {
            try
            {
                string statusText;
                if (!string.IsNullOrEmpty(customMessage))
                {
                    statusText = customMessage;
                }
                else
                {
                    var spotStatus = _isSpotConnected ? "现货✓" : "现货✗";
                    var futuresStatus = _isFuturesConnected ? "期货✓" : "期货✗";
                    var marketStatus = _futuresPriceService?.IsMarketOpen == true ? "开市" : "休市";
                    statusText = $"{spotStatus} {futuresStatus} ({marketStatus}) {DateTime.Now:HH:mm:ss}";
                }

                UpdatePriceUpdateTime(statusText);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新连接状态时发生错误");
            }
        }

        private void UpdatePriceUpdateTime(string timeText)
        {
            if (FindName("PriceUpdateTimeText") is TextBlock timeTextBlock)
            {
                timeTextBlock.Text = timeText;
            }
        }

        private void UpdateSpotPriceDisplay(SpotPriceData priceData)
        {
            try
            {
                _logger.LogDebug("更新现货价格显示: 买入价={BuyPrice}, 卖出价={SellPrice}",
                    priceData.BuyPrice, priceData.SellPrice);

                // 更新现货买入价
                var spotBuyPriceText = FindName("SpotBuyPriceText") as TextBlock;
                var spotBuyIndicator = FindName("SpotBuyIndicator") as TextBlock;
                if (spotBuyPriceText != null)
                {
                    spotBuyPriceText.Text = priceData.BuyPrice.ToString("F2");

                    // 颜色现在通过数据绑定设置，不需要在这里手动设置
                    // 保留指示器逻辑
                    if (_lastSpotPriceData != null && spotBuyIndicator != null)
                    {
                        if (priceData.BuyPrice > _lastSpotPriceData.BuyPrice)
                        {
                            // spotBuyPriceText.Foreground = new SolidColorBrush(Colors.Red); // 已通过绑定设置
                            spotBuyIndicator.Text = "↑";
                            spotBuyIndicator.Foreground = new SolidColorBrush(Colors.Red);
                        }
                        else if (priceData.BuyPrice < _lastSpotPriceData.BuyPrice)
                        {
                            // spotBuyPriceText.Foreground = new SolidColorBrush(Colors.Green); // 已通过绑定设置
                            spotBuyIndicator.Text = "↓";
                            spotBuyIndicator.Foreground = new SolidColorBrush(Colors.Green);
                        }
                    }
                }

                // 更新现货卖出价
                var spotSellPriceText = FindName("SpotSellPriceText") as TextBlock;
                var spotSellIndicator = FindName("SpotSellIndicator") as TextBlock;
                if (spotSellPriceText != null)
                {
                    spotSellPriceText.Text = priceData.SellPrice.ToString("F2");

                    // 颜色现在通过数据绑定设置，不需要在这里手动设置
                    // 保留指示器逻辑
                    if (_lastSpotPriceData != null && spotSellIndicator != null)
                    {
                        if (priceData.SellPrice > _lastSpotPriceData.SellPrice)
                        {
                            // spotSellPriceText.Foreground = new SolidColorBrush(Colors.Red); // 已通过绑定设置
                            spotSellIndicator.Text = "↑";
                            spotSellIndicator.Foreground = new SolidColorBrush(Colors.Red);
                        }
                        else if (priceData.SellPrice < _lastSpotPriceData.SellPrice)
                        {
                            // spotSellPriceText.Foreground = new SolidColorBrush(Colors.Green); // 已通过绑定设置
                            spotSellIndicator.Text = "↓";
                            spotSellIndicator.Foreground = new SolidColorBrush(Colors.Green);
                        }
                    }
                }

                _lastSpotPriceData = priceData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新现货价格显示失败");
            }
        }

        /// <summary>
        /// 更新期货价格显示
        /// </summary>
        private void UpdateFuturesPriceDisplay(FuturesPriceData priceData)
        {
            try
            {
                _logger.LogDebug($"开始更新期货价格显示: {priceData.Symbol}");
                
                // 检查是否为选中的合约
                if (priceData.Symbol != _selectedContract)
                {
                    _logger.LogDebug($"跳过非选中合约: {priceData.Symbol}, 当前选中: {_selectedContract}");
                    return;
                }
                
                // 验证价格数据有效性
                if (!priceData.IsValidPrice)
                {
                    _logger.LogWarning($"价格数据无效: 买入={priceData.BuyPrice}, 卖出={priceData.SellPrice}");
                    return;
                }
                
                _logger.LogInformation($"更新期货价格显示: 买入={priceData.BuyPrice:F2}, 卖出={priceData.SellPrice:F2}, 最新={priceData.LastPrice:F2}");
                
                // 更新买入价显示
                var futuresBuyPriceText = FindName("FuturesBuyPriceText") as TextBlock;
                if (futuresBuyPriceText != null)
                {
                    var buyPriceText = $"{priceData.BuyPrice:F2}";
                    futuresBuyPriceText.Text = buyPriceText;
                    _logger.LogDebug($"更新期货买入价: {buyPriceText}");
                }
                else
                {
                    _logger.LogWarning("FuturesBuyPriceText控件为null");
                }
                
                // 更新卖出价显示
                var futuresSellPriceText = FindName("FuturesSellPriceText") as TextBlock;
                if (futuresSellPriceText != null)
                {
                    var sellPriceText = $"{priceData.SellPrice:F2}";
                    futuresSellPriceText.Text = sellPriceText;
                    _logger.LogDebug($"更新期货卖出价: {sellPriceText}");
                }
                else
                {
                    _logger.LogWarning("FuturesSellPriceText控件为null");
                }
                
                // 更新价格趋势指示器
                UpdatePriceTrendIndicator(priceData);

                // 更新PriceDisplayViewModel中的期货价格趋势属性，用于XAML样式绑定
                UpdateFuturesPriceTrendInViewModel(priceData);

                // 更新数据质量指示器
                UpdateDataQualityIndicator(priceData);
                
                // 缓存数据
                _lastFuturesPriceData = priceData;
                
                _logger.LogDebug("期货价格显示更新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新期货价格显示时发生错误");
            }
        }
        
        /// <summary>
        /// 更新价格趋势指示器
        /// </summary>
        private void UpdatePriceTrendIndicator(FuturesPriceData priceData)
        {
            try
            {
                // 注释掉直接设置颜色的代码，改为使用XAML样式控制
                // 根据价格趋势设置颜色
                // var trendBrush = priceData.Trend switch
                // {
                //     PriceTrend.Up => Brushes.Red,
                //     PriceTrend.Down => Brushes.Green,
                //     _ => Brushes.Gray
                // };

                // 注释掉直接设置Foreground的代码，避免与XAML样式冲突
                // 应用到买入价和卖出价文本框
                // var futuresBuyPriceText = FindName("FuturesBuyPriceText") as TextBlock;
                // if (futuresBuyPriceText != null)
                // {
                //     futuresBuyPriceText.Foreground = trendBrush;
                // }

                // var futuresSellPriceText = FindName("FuturesSellPriceText") as TextBlock;
                // if (futuresSellPriceText != null)
                // {
                //     futuresSellPriceText.Foreground = trendBrush;
                // }

                // 现在颜色由XAML样式中的FuturesPriceTextStyle控制
                // 基于PriceDisplayViewModel.FuturesPriceIncreasing属性
                
                _logger.LogTrace($"价格趋势指示器更新: {priceData.Trend}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新价格趋势指示器时发生错误");
            }
        }

        /// <summary>
        /// 更新PriceDisplayViewModel中的期货价格趋势属性
        /// </summary>
        private void UpdateFuturesPriceTrendInViewModel(FuturesPriceData priceData)
        {
            try
            {
                _logger.LogInformation($"🎯 开始更新期货价格趋势ViewModel属性，趋势: {priceData.Trend}");
                if (PriceDisplayViewModel != null)
                {
                    // 根据期货价格趋势更新ViewModel属性
                    var trendString = priceData.Trend.ToString();
                    if (trendString == "Up" || trendString == "Rising")
                    {
                        PriceDisplayViewModel.FuturesPriceIncreasing = true;  // 上涨显示红色
                        _logger.LogInformation($"🔴 期货价格趋势: 上涨 ({trendString}) -> 红色");
                    }
                    else if (trendString == "Down" || trendString == "Falling")
                    {
                        PriceDisplayViewModel.FuturesPriceIncreasing = false; // 下跌显示绿色
                        _logger.LogInformation($"🟢 期货价格趋势: 下跌 ({trendString}) -> 绿色");
                    }
                    else
                    {
                        // 稳定或无变化时，保持之前的颜色，不要重新计算
                        _logger.LogInformation($"⏸️ 期货价格趋势: 稳定 ({trendString})，保持之前颜色 -> {(PriceDisplayViewModel.FuturesPriceIncreasing ? "红色" : "绿色")}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新期货价格趋势ViewModel属性时发生错误");
            }
        }

        /// <summary>
        /// 更新数据质量指示器
        /// </summary>
        private void UpdateDataQualityIndicator(FuturesPriceData priceData)
        {
            try
            {
                var qualityIndicator = priceData.DataQuality switch
                {
                    "realtime" => "●",  // 实时数据
                    "cached" => "○",    // 缓存数据
                    "mock" => "△",      // 模拟数据
                    _ => "?"            // 未知数据
                };
                
                _logger.LogTrace($"数据质量指示器: {qualityIndicator} ({priceData.DataQuality})");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新数据质量指示器时发生错误");
            }
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 处理期货价格更新事件
        /// </summary>
        private void OnFuturesPriceUpdated(object? sender, FuturesPriceData priceData)
        {
            try
            {
                _logger.LogInformation($"收到天勤SDK期货价格更新事件: {priceData.Symbol}, 买入={priceData.BuyPrice:F2}, 卖出={priceData.SellPrice:F2}, 数据源={priceData.Source}");

                // 🔧 修复死锁：确保在UI线程上执行更新，避免递归调用
                if (!Dispatcher.CheckAccess())
                {
                    _logger.LogDebug("在非UI线程，调度到UI线程执行");
                    Dispatcher.BeginInvoke(() => UpdateFuturesPriceDisplaySafe(priceData));
                    return;
                }

                _logger.LogDebug("在UI线程上执行天勤SDK期货价格更新");

                // 🔧 直接调用安全的更新方法
                UpdateFuturesPriceDisplaySafe(priceData);

                // 记录数据质量信息
                if (!string.IsNullOrEmpty(priceData.DataQuality))
                {
                    _logger.LogDebug($"天勤SDK数据质量: {priceData.DataQuality}, 市场状态: {priceData.MarketStatus}");
                }

                // 如果应用已初始化，更新套利信息
                if (_isInitialized)
                {
                    _logger.LogDebug("更新套利信息");
                    UpdateArbitrageInfo();
                }
                else
                {
                    _logger.LogDebug("应用未完全初始化，跳过套利信息更新");
                }

                // 发送期货价格更新消息给持仓订单管理器
                var messenger = ((App)Application.Current).ServiceProvider?.GetService<IMessenger>();
                if (messenger != null)
                {
                    var futuresPriceDataResponse = new FuturesPriceDataResponse
                    {
                        ContractCode = priceData.Symbol ?? "SHFE.au2510",
                        BidPrice = priceData.BuyPrice,
                        AskPrice = priceData.SellPrice,
                        LastPrice = priceData.LastPrice,
                        Volume = priceData.Volume,
                        OpenInterest = priceData.OpenInterest,
                        UpdateTime = priceData.UpdateTime,
                        IsOnline = true
                    };
                    var futuresPriceMessage = new FuturesPriceUpdateMessage(futuresPriceDataResponse);
                    messenger.Send(futuresPriceMessage);
                    _logger.LogInformation("✅ 已发送期货价格更新消息: 合约={Symbol}, 买入={BuyPrice:F2}, 卖出={SellPrice:F2}",
                        priceData.Symbol, priceData.BuyPrice, priceData.SellPrice);
                }

                _logger.LogDebug("天勤SDK期货价格更新处理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理天勤SDK期货价格更新事件时发生错误");
            }
        }

        /// <summary>
        /// 安全的期货价格显示更新方法（避免递归调用）
        /// </summary>
        private void UpdateFuturesPriceDisplaySafe(FuturesPriceData priceData)
        {
            try
            {
                // 更新期货价格显示
                UpdateFuturesPriceDisplay(priceData);

                // 缓存最新数据
                _lastFuturesPriceData = priceData;

                // 确保连接状态为已连接
                if (!_isFuturesConnected)
                {
                    _logger.LogInformation("设置天勤SDK期货连接状态为已连接");
                    _isFuturesConnected = true;
                    UpdateConnectionStatus();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安全更新期货价格显示失败");
            }
        }

        /// <summary>
        /// 合约变更事件处理
        /// </summary>
        private void OnContractChanged(object? sender, string newContract)
        {
            try
            {
                _selectedContract = newContract;
                _logger.LogInformation("合约已切换到: {Contract}", newContract);

                // 清除当前期货价格数据，等待新合约的数据
                _lastFuturesPriceData = null;
                _isFuturesConnected = false;

                // 立即尝试获取新合约的价格数据
                var latestPrice = _futuresPriceService?.GetLatestPrice();
                if (latestPrice != null)
                {
                    _logger.LogInformation("立即获取到新合约价格数据: {Data}", latestPrice.ToDisplayString());
                    OnFuturesPriceUpdated(this, latestPrice);
                }
                else
                {
                    _logger.LogInformation("等待新合约价格数据: {Contract}", newContract);
                    UpdateConnectionStatus("等待新合约数据...");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理合约变更时发生错误");
            }
        }

        /// <summary>
        /// 市场状态变化事件处理
        /// </summary>
        private void OnMarketStatusChanged(object? sender, bool isMarketOpen)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    _logger.LogInformation("期货市场状态变更: {Status}", isMarketOpen ? "开市" : "休市");

                    if (!isMarketOpen)
                    {
                        // 休市时显示休市状态
                        UpdateMarketClosedStatus();
                    }
                    else
                    {
                        // 开市时清除休市状态，恢复正常显示
                        ClearMarketClosedStatus();
                    }

                    UpdateConnectionStatus();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理市场状态变化时发生错误");
                }
            });
        }

        /// <summary>
        /// 更新休市状态显示
        /// </summary>
        private void UpdateMarketClosedStatus()
        {
            try
            {
                // 更新期货买入价显示为休市状态
                var futuresBuyPriceText = FindName("FuturesBuyPriceText") as TextBlock;
                var futuresBuyIndicator = FindName("FuturesBuyIndicator") as TextBlock;
                if (futuresBuyPriceText != null)
                {
                    futuresBuyPriceText.Text = "休市";
                    // futuresBuyPriceText.Foreground = new SolidColorBrush(Colors.Gray); // 颜色通过趋势绑定设置
                }
                if (futuresBuyIndicator != null)
                {
                    futuresBuyIndicator.Text = "";
                }

                // 更新期货卖出价显示为休市状态
                var futuresSellPriceText = FindName("FuturesSellPriceText") as TextBlock;
                var futuresSellIndicator = FindName("FuturesSellIndicator") as TextBlock;
                if (futuresSellPriceText != null)
                {
                    futuresSellPriceText.Text = "休市";
                    // futuresSellPriceText.Foreground = new SolidColorBrush(Colors.Gray); // 颜色通过趋势绑定设置
                }
                if (futuresSellIndicator != null)
                {
                    futuresSellIndicator.Text = "";
                }

                _isFuturesConnected = false;
                _logger.LogInformation("已更新期货价格显示为休市状态");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新休市状态显示时发生错误");
            }
        }

        /// <summary>
        /// 清除休市状态显示
        /// </summary>
        private void ClearMarketClosedStatus()
        {
            try
            {
                _logger.LogInformation("期货市场开市，恢复价格数据显示");
                
                // 确保在UI线程上执行
                Dispatcher.Invoke(() =>
                {
                    // 设置连接状态为true
                    _isFuturesConnected = true;
                    
                    // 立即尝试获取当前价格数据
                    var latestPrice = _futuresPriceService?.GetLatestPrice();
                    if (latestPrice != null && latestPrice.IsValidPrice)
                    {
                        _logger.LogInformation("市场开市 - 获取到期货价格数据: 买入={BuyPrice:F2}, 卖出={SellPrice:F2}", 
                            latestPrice.BuyPrice, latestPrice.SellPrice);
                        
                        // 直接更新UI显示
                        UpdateFuturesPriceDisplay(latestPrice);
                        _lastFuturesPriceData = latestPrice;
                        
                        if (_isInitialized)
                        {
                            UpdateArbitrageInfo();
                        }
                    }
                    else
                    {
                        _logger.LogInformation("市场开市 - 等待期货价格数据更新...");
                        
                        // 清除休市状态，显示等待状态
                        var futuresBuyPriceText = FindName("FuturesBuyPriceText") as TextBlock;
                        var futuresSellPriceText = FindName("FuturesSellPriceText") as TextBlock;
                        
                        if (futuresBuyPriceText != null)
                        {
                            futuresBuyPriceText.Text = "连接中...";
                            futuresBuyPriceText.Foreground = new SolidColorBrush(Colors.Blue);
                        }
                        
                        if (futuresSellPriceText != null)
                        {
                            futuresSellPriceText.Text = "连接中...";
                            futuresSellPriceText.Foreground = new SolidColorBrush(Colors.Blue);
                        }
                        
                        // 强制检查并读取数据文件
                        _ = Task.Run(async () =>
                        {
                            await Task.Delay(1000); // 等待1秒
                            var dataFilePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Services", "TqSdkPythonService", "tqsdk_data.json");
                            if (File.Exists(dataFilePath))
                            {
                                _logger.LogInformation("强制重新读取期货数据文件");
                                // 这里可以触发期货服务重新读取数据文件
                                var updatedPrice = _futuresPriceService?.GetLatestPrice();
                                if (updatedPrice != null && updatedPrice.IsValidPrice)
                                {
                                    _ = Dispatcher.InvokeAsync(() =>
                                    {
                                        UpdateFuturesPriceDisplay(updatedPrice);
                                        _lastFuturesPriceData = updatedPrice;
                                        if (_isInitialized)
                                        {
                                            UpdateArbitrageInfo();
                                        }
                                    });
                                }
                            }
                        });
                    }
                    
                    // 更新连接状态显示
                    UpdateConnectionStatus();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除休市状态显示时发生错误");
            }
        }

        /// <summary>
        /// 初始化合约选择器
        /// </summary>
        private void InitializeContractSelector()
        {
            try
            {
                if (FindName("ContractSelector") is ComboBox contractSelector)
                {
                    contractSelector.ItemsSource = _contractSelectorViewModel.AvailableContracts;
                    contractSelector.SelectedValue = _selectedContract;
                    _logger.LogInformation("合约选择器已初始化，默认合约: {Contract}", _selectedContract);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化合约选择器时发生错误");
            }
        }

        /// <summary>
        /// 合约选择器变更事件
        /// </summary>
        private void ContractSelector_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (sender is ComboBox comboBox && comboBox.SelectedValue is string selectedContract)
                {
                    _contractSelectorViewModel.SelectedContract = selectedContract;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理合约选择器变更时发生错误");
            }
        }

        /// <summary>
        /// 更新套利信息
        /// </summary>
        private void UpdateArbitrageInfo()
        {
            if (_lastSpotPriceData == null || _lastFuturesPriceData == null || !_lastFuturesPriceData.IsValidPrice)
                return;

            try
            {
                // 计算套利价差
                var forwardSpread = _lastFuturesPriceData.BuyPrice - _lastSpotPriceData.SellPrice;
                var reverseSpread = _lastSpotPriceData.BuyPrice - _lastFuturesPriceData.SellPrice;

                // 更新套利信息显示（如果有相关控件的话）
                _logger.LogDebug("套利价差计算 - 正向: {ForwardSpread:F2}, 反向: {ReverseSpread:F2}", 
                    forwardSpread, reverseSpread);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新套利信息失败");
            }
        }

        #region 历史基差视图管理

        /// <summary>
        /// 加载历史基差视图
        /// </summary>
        private async void LoadBasisHistoryView()
        {
            try
            {
                var contentPresenter = FindName("BasisHistoryContentPresenter") as ContentPresenter;
                if (contentPresenter != null)
                {
                    // 从服务容器获取BasisHistoryViewModel
                    var viewModel = _serviceProvider.GetService<BasisHistoryViewModel>();
                    if (viewModel != null)
                    {
                        // 如果视图还没有创建，创建新视图
                        if (contentPresenter.Content == null)
                        {
                            var view = new Views.BasisHistoryView(viewModel);
                            contentPresenter.Content = view;
                            _logger.LogDebug("历史基差视图已创建");
                        }

                        // 🎯 关键修复：每次切换到历史基差页面时都自动刷新数据
                        _logger.LogInformation("🔄 用户切换到历史基差页面，开始自动刷新数据...");

                        // 异步执行数据加载，避免阻塞UI
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await viewModel.TriggerDataLoadAsync();
                                _logger.LogInformation("✅ 历史基差数据自动刷新完成");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "❌ 历史基差数据自动刷新失败");
                            }
                        });
                    }
                    else
                    {
                        _logger.LogWarning("无法获取BasisHistoryViewModel服务");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载历史基差视图失败");
            }
        }

        #endregion

        #region 导航事件处理器

        /// <summary>
        /// 统一的导航按钮点击事件处理器
        /// </summary>
        private void OnNavigationButtonClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button)
            {
                string targetTab = button.Name switch
                {
                    "TradingNavButton" => "trading",
                    "OrdersNavButton" => "orders",
                    "TradeMonitorNavButton" => "trademonitor",
                    "BasisHistoryNavButton" => "basishistory", // 🆕 历史基差标签页
                    "SettingsNavButton" => "settings",
                    _ => "trading"
                };

                SwitchToTab(targetTab);
            }
        }

        /// <summary>
        /// 切换到指定标签页
        /// </summary>
        private void SwitchToTab(string tabName)
        {
            try
            {
                // 更新导航按钮状态
                UpdateNavigationButtonStates(tabName);

                // 实现标签页内容切换逻辑
                var tradingTab = FindName("TradingTabContent") as FrameworkElement;
                var ordersTab = FindName("OrdersTabContent") as FrameworkElement;
                var tradeMonitorTab = FindName("TradeMonitorTabContent") as FrameworkElement;
                var basisHistoryTab = FindName("BasisHistoryTabContent") as FrameworkElement; // 🆕 历史基差标签页
                var settingsTab = FindName("SettingsTabContent") as FrameworkElement;

                // 隐藏所有标签页
                if (tradingTab != null) tradingTab.Visibility = Visibility.Collapsed;
                if (ordersTab != null) ordersTab.Visibility = Visibility.Collapsed;
                if (tradeMonitorTab != null) tradeMonitorTab.Visibility = Visibility.Collapsed;
                if (basisHistoryTab != null) basisHistoryTab.Visibility = Visibility.Collapsed; // 🆕
                if (settingsTab != null) settingsTab.Visibility = Visibility.Collapsed;

                switch (tabName)
                {
                    case "trading":
                        if (tradingTab != null) tradingTab.Visibility = Visibility.Visible;
                        break;
                    case "orders":
                        if (ordersTab != null) ordersTab.Visibility = Visibility.Visible;
                        break;
                    case "trademonitor":
                        if (tradeMonitorTab != null) tradeMonitorTab.Visibility = Visibility.Visible;
                        break;
                    case "basishistory": // 🆕 历史基差标签页
                        if (basisHistoryTab != null)
                        {
                            basisHistoryTab.Visibility = Visibility.Visible;
                            LoadBasisHistoryView(); // 加载历史基差视图
                        }
                        break;
                    case "settings":
                        if (settingsTab != null) settingsTab.Visibility = Visibility.Visible;
                        break;
                }

                _logger.LogDebug("已切换到标签页: {TabName}", tabName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换标签页时发生错误: {TabName}", tabName);
            }
        }

        /// <summary>
        /// 更新导航按钮状态
        /// </summary>
        private void UpdateNavigationButtonStates(string activePage)
        {
            try
            {
                var buttons = new[]
                {
                    ("TradingNavButton", "trading"),
                    ("OrdersNavButton", "orders"),
                    ("TradeMonitorNavButton", "trademonitor"),
                    ("BasisHistoryNavButton", "basishistory"), // 🆕 历史基差按钮
                    ("SettingsNavButton", "settings")
                };

                foreach (var (buttonName, pageName) in buttons)
                {
                    if (FindName(buttonName) is Button button)
                    {
                        button.Tag = pageName == activePage ? "Active" : null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新导航按钮状态时发生错误");
            }
        }

        // 具体的导航按钮事件处理器
        private void TradingNavButton_Click(object sender, RoutedEventArgs e) => SwitchToTab("trading");
        private void StatisticsNavButton_Click(object sender, RoutedEventArgs e) => SwitchToTab("orders");
        private void SettingsNavButton_Click(object sender, RoutedEventArgs e) => SwitchToTab("settings");

        #endregion

        #region 交易配置相关事件

        /// <summary>
        /// 编辑交易配置按钮点击事件
        /// </summary>
        private void EditTradingConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("用户点击编辑交易配置按钮");

                // 显示交易配置编辑对话框
                var dialog = new Views.TradingConfigEditDialog
                {
                    Owner = this
                };

                var result = dialog.ShowDialog();

                if (result == true)
                {
                    _logger.LogInformation("交易配置编辑完成");

                    // 可以在这里添加配置更新后的处理逻辑
                    // 例如刷新UI显示的配置数据
                    RefreshTradingConfigDisplay();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开交易配置编辑对话框时发生错误");
                MessageBox.Show($"打开配置编辑失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 刷新交易配置显示
        /// </summary>
        private async void RefreshTradingConfigDisplay()
        {
            try
            {
                _logger.LogDebug("刷新交易配置显示");

                // 获取交易配置服务
                var app = (App)Application.Current;
                var tradingConfigService = app.ServiceProvider.GetService<ITradingConfigService>();
                if (tradingConfigService != null)
                {
                    // 获取最新的交易配置显示数据
                    var displayModel = await tradingConfigService.GetTradingConfigDisplayAsync(1); // 使用用户ID 1

                    // 更新UI显示
                    if (this.FindName("PositionSizeText") is TextBlock positionSizeText)
                        positionSizeText.Text = displayModel.PositionSize.ToString();

                    if (this.FindName("BaseWeightText") is TextBlock baseWeightText)
                        baseWeightText.Text = $"{displayModel.BaseWeight}g";

                    if (this.FindName("SpotCostText") is TextBlock spotCostText)
                        spotCostText.Text = displayModel.SpotCost.ToString("F2");

                    if (this.FindName("FutureCostText") is TextBlock futureCostText)
                        futureCostText.Text = displayModel.FutureCost.ToString("F2");

                    _logger.LogInformation("交易配置显示已更新");
                }

                // 显示刷新成功的提示
                ShowOperationMessage("✅", "配置已更新", "#28a745", "#d4edda");

                // 3秒后隐藏提示
                HideOperationMessageAfterDelay(3000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新交易配置显示时发生错误");
                ShowOperationMessage("❌", "配置更新失败", "#dc3545", "#f8d7da");
            }
        }

        #endregion

        protected override async void OnClosed(EventArgs e)
        {
            try
            {
                _logger.LogInformation("正在关闭主窗口...");

                // 1. 停止应用程序服务（包括API服务器、SignalR连接等）
                var app = (App)Application.Current;
                var serviceProvider = app.ServiceProvider;
                var applicationStartupService = serviceProvider.GetService<IApplicationStartupService>();

                if (applicationStartupService != null)
                {
                    try
                    {
                        await applicationStartupService.StopAsync();
                        _logger.LogInformation("✅ 应用程序服务已停止");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "停止应用程序服务时发生错误");
                    }
                }

                // 2. 停止现货价格服务
                if (_spotPriceService != null)
                {
                    try
                    {
                        await _spotPriceService.DisconnectAsync();
                        _logger.LogInformation("✅ 现货价格服务已断开");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "断开现货价格服务时发生错误");
                    }
                }

                // 3. 释放期货价格服务资源
                try
                {
                    _futuresPriceService?.Dispose();
                    _logger.LogInformation("✅ 期货价格服务已释放");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放期货价格服务时发生错误");
                }

                // 4. 停止定时器
                try
                {
                    _uiUpdateTimer?.Stop();
                    _connectionCheckTimer?.Stop();
                    _logger.LogInformation("✅ 定时器已停止");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "停止定时器时发生错误");
                }

                _logger.LogInformation("🎉 主窗口关闭完成，所有服务已清理");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭主窗口时发生错误");
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        private void InitializeUIState()
        {
            try {
                _logger.LogInformation("初始化UI控件状态...");
                
                // 强制确保UI线程上执行
                Dispatcher.Invoke(() => {
                    // 现货价格初始化 - 暂时注释掉，因为控件不存在
                    // TODO: 实现现货价格UI更新逻辑
                    _logger.LogInformation("现货价格UI状态设置为连接中...");
                    
                    // 期货价格初始化 - 关键修复点！
                    var futuresBuyControl = FindName("FuturesBuyPriceText") as TextBlock;
                    var futuresSellControl = FindName("FuturesSellPriceText") as TextBlock;
                    
                    if (futuresBuyControl != null) {
                        futuresBuyControl.Text = "连接中...";
                        // futuresBuyControl.Foreground = new SolidColorBrush(Colors.Blue); // 颜色通过趋势绑定设置
                        _logger.LogInformation("期货买入价控件初始化完成");
                    } else {
                        _logger.LogError("无法找到FuturesBuyPriceText控件！");
                    }

                    if (futuresSellControl != null) {
                        futuresSellControl.Text = "连接中...";
                        // futuresSellControl.Foreground = new SolidColorBrush(Colors.Blue); // 颜色通过趋势绑定设置
                        _logger.LogInformation("期货卖出价控件初始化完成");
                    } else {
                        _logger.LogError("无法找到FuturesSellPriceText控件！");
                    }
                    
                    _logger.LogInformation("UI控件状态初始化完成");
                });
                
            } catch (Exception ex) {
                _logger.LogError(ex, "初始化UI状态时发生错误");
            }
        }

        private void OnUIUpdateTimerTick(object sender, EventArgs e)
        {
            try {
                // 强制检查和更新UI状态
                CheckAndUpdateConnectionStatus();
                
                // 如果数据超过30秒未更新，显示警告
                var now = DateTime.Now;
                if ((now - _lastFuturesUpdate).TotalSeconds > 30 && _isFuturesConnected) {
                    UpdateFuturesUIWithError("数据延迟");
                }
                if ((now - _lastSpotUpdate).TotalSeconds > 30 && _isSpotConnected) {
                    UpdateSpotUIWithError("数据延迟");
                }
                
            } catch (Exception ex) {
                _logger.LogError(ex, "UI更新定时器异常");
            }
        }

        private void OnConnectionCheckTimerTick(object sender, EventArgs e)
        {
            try {
                // 检查服务连接状态
                var spotStatus = _spotPriceService?.IsConnected ?? false;
                var futuresStatus = _futuresPriceService?.IsConnected ?? false;
                
                if (spotStatus != _isSpotConnected) {
                    _isSpotConnected = spotStatus;
                    _logger.LogInformation($"现货连接状态变更: {spotStatus}");
                }
                
                if (futuresStatus != _isFuturesConnected) {
                    _isFuturesConnected = futuresStatus;
                    _logger.LogInformation($"期货连接状态变更: {futuresStatus}");
                }
                
            } catch (Exception ex) {
                _logger.LogError(ex, "连接状态检查异常");
            }
        }

        private void CheckAndUpdateConnectionStatus()
        {
            try {
                // 检查期货数据文件是否存在且最近更新
                var futuresDataFile = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "futures_data.json");
                var futuresConnected = File.Exists(futuresDataFile) && 
                                      (DateTime.Now - File.GetLastWriteTime(futuresDataFile)).TotalSeconds < 10;
                
                // 更新连接状态指示
                if (!futuresConnected && _isFuturesConnected) {
                    _isFuturesConnected = false;
                    UpdateFuturesUIWithError("连接中断");
                } else if (futuresConnected && !_isFuturesConnected) {
                    _isFuturesConnected = true;
                    _logger.LogInformation("期货连接已恢复");
                }
            } catch (Exception ex) {
                _logger.LogError(ex, "检查连接状态时发生错误");
            }
        }

        private void OnSpotPriceUpdated(object sender, SpotPriceUpdatedEventArgs e)
        {
            try
            {
                // 检查应用程序状态
                if (Application.Current == null || Dispatcher == null || Dispatcher.HasShutdownStarted)
                {
                    return;
                }

                _logger.LogInformation($"收到现货价格更新事件: 买入价={e.Bid}, 卖出价={e.Ask}");

                _lastSpotUpdate = DateTime.Now;
                _isSpotConnected = true;

                Dispatcher.Invoke(() => UpdateSpotPriceUI(e.Bid, e.Ask));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理现货价格更新事件时发生错误");
            }
        }

        private void OnFuturesPriceUpdated(object? sender, FuturePriceUpdatedEventArgs e)
        {
            try
            {
                // 检查应用程序状态
                if (Application.Current == null || Dispatcher == null || Dispatcher.HasShutdownStarted)
                {
                    return;
                }

                _logger.LogInformation($"收到期货价格更新事件: 买入价={e.Bid}, 卖出价={e.Ask}");

                _lastFuturesUpdate = DateTime.Now;
                _isFuturesConnected = true;

                // 从数据文件中读取价格变化信息（Python脚本已经计算好了）
                var changeAmount = 0m;
                var changePercent = 0m;
                var trend = "Stable";

                try
                {
                    var dataFilePath = "futures_data.json";
                    if (File.Exists(dataFilePath))
                    {
                        var jsonContent = File.ReadAllText(dataFilePath);
                        var jsonDoc = JsonDocument.Parse(jsonContent);
                        var root = jsonDoc.RootElement;

                        if (root.TryGetProperty("ChangeAmount", out var changeAmountElement))
                        {
                            changeAmount = changeAmountElement.GetDecimal();
                        }
                        if (root.TryGetProperty("ChangePercent", out var changePercentElement))
                        {
                            changePercent = changePercentElement.GetDecimal();
                        }
                        if (root.TryGetProperty("Trend", out var trendElement))
                        {
                            trend = trendElement.GetString() ?? "Stable";
                        }

                        _logger.LogDebug($"从数据文件读取: ChangeAmount={changeAmount:F2}, Trend={trend}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "读取期货数据文件失败，使用默认值");
                    // 如果读取失败，回退到原来的计算方式
                    if (_lastFuturesPriceData != null)
                    {
                        changeAmount = e.LastPrice - _lastFuturesPriceData.LastPrice;
                        changePercent = _lastFuturesPriceData.LastPrice != 0 ? changeAmount / _lastFuturesPriceData.LastPrice * 100 : 0;
                    }
                }

                // 将字符串趋势转换为PriceTrend枚举
                PriceTrend priceTrend = PriceTrend.Stable; // 默认值
                if (Enum.TryParse<PriceTrend>(trend, true, out var parsedTrend))
                {
                    priceTrend = parsedTrend;
                }
                else
                {
                    // 处理Python脚本中的趋势字符串映射
                    switch (trend.ToLower())
                    {
                        case "rising":
                        case "up":
                            priceTrend = PriceTrend.Up;
                            break;
                        case "falling":
                        case "down":
                            priceTrend = PriceTrend.Down;
                            break;
                        case "stable":
                        case "unchanged":
                        default:
                            priceTrend = PriceTrend.Stable;
                            break;
                    }
                }

                _logger.LogDebug($"趋势转换: '{trend}' -> {priceTrend}");

                // 创建FuturesPriceData对象用于UI更新
                var futuresData = new FuturesPriceData
                {
                    Symbol = e.ContractCode,
                    BuyPrice = e.Bid,
                    SellPrice = e.Ask,
                    LastPrice = e.LastPrice,
                    Volume = (long)e.Volume,
                    OpenInterest = e.OpenInterest,
                    ChangeAmount = Math.Round(changeAmount, 2),
                    ChangePercent = Math.Round(changePercent, 2),
                    UpdateTime = e.Timestamp,
                    Trend = priceTrend // 设置正确的趋势枚举值
                };

                // 强制在UI线程上执行更新
                Dispatcher.BeginInvoke(new Action(() => {
                    UpdateFuturesPriceUI(futuresData);
                }), DispatcherPriority.Normal);

                // 发送消息给PriceDisplayViewModel
                var app = Application.Current as App;
                var messenger = app?.ServiceProvider?.GetService<IMessenger>();
                if (messenger != null)
                {
                    var futuresResponse = new FuturesPriceDataResponse
                    {
                        ContractCode = futuresData.Symbol ?? "SHFE.au2510",
                        BidPrice = futuresData.BuyPrice,
                        AskPrice = futuresData.SellPrice,
                        LastPrice = futuresData.LastPrice,
                        Volume = futuresData.Volume,
                        OpenInterest = futuresData.OpenInterest,
                        UpdateTime = futuresData.UpdateTime,
                        Trend = trend, // 使用从数据文件中读取的趋势
                        IsOnline = true
                    };

                    messenger.Send(new FuturesPriceUpdateMessage(futuresResponse));
                    _logger.LogDebug("已发送期货价格更新消息到PriceDisplayViewModel");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理期货价格更新事件时发生错误");
            }
        }

        private void UpdateSpotPriceUI(decimal bidPrice, decimal askPrice)
        {
            try
            {
                _logger.LogInformation($"更新现货价格UI: 买入价={bidPrice}, 卖出价={askPrice}");

                // TODO: 实现现货价格UI更新逻辑
                // 暂时只记录日志，因为控件不存在
                _logger.LogInformation("现货价格UI更新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新现货价格UI时发生错误");
            }
        }

        private void UpdateFuturesPriceUI(FuturesPriceData data)
        {
            try
            {
                _logger.LogInformation($"开始更新期货价格UI: 买入价={data.BuyPrice}, 卖出价={data.SellPrice}");

                // 检查数据有效性
                if (data.BuyPrice <= 0 || data.SellPrice <= 0)
                {
                    _logger.LogWarning($"期货价格数据无效: bid={data.BuyPrice}, ask={data.SellPrice}");
                    UpdateFuturesUIWithError("数据无效");
                    return;
                }
                
                // 使用FindName方式获取控件引用
                var futuresBuyText = FindName("FuturesBuyPriceText") as TextBlock;
                var futuresSellText = FindName("FuturesSellPriceText") as TextBlock;
                
                if (futuresBuyText != null)
                {
                    var buyPriceText = data.BuyPrice.ToString("F2");
                    futuresBuyText.Text = buyPriceText;
                    _logger.LogInformation($"期货买入价UI已更新: {buyPriceText}");
                }
                else
                {
                    _logger.LogError("FuturesBuyPriceText控件仍然为null，无法更新UI！");
                }

                if (futuresSellText != null)
                {
                    var sellPriceText = data.SellPrice.ToString("F2");
                    futuresSellText.Text = sellPriceText;
                    _logger.LogInformation($"期货卖出价UI已更新: {sellPriceText}");
                }
                else
                {
                    _logger.LogError("FuturesSellPriceText控件仍然为null，无法更新UI！");
                }

                // 更新价格趋势颜色
                UpdatePriceTrendIndicator(data);

                // 更新PriceDisplayViewModel中的期货价格趋势属性，用于XAML样式绑定
                UpdateFuturesPriceTrendInViewModel(data);

                // 强制UI刷新
                this.UpdateLayout();
                
                _logger.LogInformation("期货价格UI更新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新期货价格UI时发生错误");
                UpdateFuturesUIWithError("更新失败");
            }
        }

        private void UpdateFuturesUIWithError(string errorMessage)
        {
            try
            {
                var futuresBuyText = FindName("FuturesBuyPriceText") as TextBlock;
                var futuresSellText = FindName("FuturesSellPriceText") as TextBlock;
                
                if (futuresBuyText != null)
                {
                    futuresBuyText.Text = errorMessage;
                    futuresBuyText.Foreground = new SolidColorBrush(Colors.Gray);
                }
                
                if (futuresSellText != null)
                {
                    futuresSellText.Text = errorMessage;
                    futuresSellText.Foreground = new SolidColorBrush(Colors.Gray);
                }
                
                _logger.LogWarning($"期货UI显示错误状态: {errorMessage}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新期货错误状态UI时发生错误");
            }
        }

        private void UpdateSpotUIWithError(string errorMessage)
        {
            try
            {
                // TODO: 实现现货错误状态UI更新逻辑
                // 暂时只记录日志，因为控件不存在
                _logger.LogWarning($"现货UI显示错误状态: {errorMessage}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新现货错误状态UI时发生错误");
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                _logger.LogInformation("窗口正在关闭，清理资源...");
                
                // 停止定时器
                _uiUpdateTimer?.Stop();
                _connectionCheckTimer?.Stop();
                
                // 取消事件订阅
                if (_spotPriceService != null)
                {
                    _spotPriceService.PriceUpdated -= OnSpotPriceUpdated;
                    _spotPriceService.StopAsync().Wait(TimeSpan.FromSeconds(2));
                }
                
                if (_futuresPriceService != null)
                {
                    _futuresPriceService.PriceUpdated -= OnFuturesPriceUpdated;
                    _futuresPriceService.StopAsync().Wait(TimeSpan.FromSeconds(2));
                }
                
                _logger.LogInformation("资源清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "窗口关闭时清理资源发生错误");
            }
        }

        // 手动刷新按钮事件处理
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("手动刷新价格数据...");
                
                // 重新初始化UI状态
                InitializeUIState();
                
                // 强制服务重新获取数据
                if (_futuresPriceService != null)
                    await _futuresPriceService.RefreshDataAsync();
                if (_spotPriceService != null)
                    await _spotPriceService.RefreshDataAsync();
                
                _logger.LogInformation("手动刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动刷新时发生错误");
            }
        }

        /// <summary>
        /// 更新现货平台数据显示
        /// </summary>
        private void UpdateSpotPlatformsDisplay(SpotPlatformData[] platformsData)
        {
            try
            {
                // 清空现有的现货平台容器
                var spotPlatformsContainer = FindName("SpotPlatformsContainer") as Panel;
                if (spotPlatformsContainer != null)
                {
                    spotPlatformsContainer.Children.Clear();

                    // 为每个平台创建UI元素
                    foreach (var platform in platformsData)
                    {
                        var platformGrid = CreateSpotPlatformRow(platform);
                        spotPlatformsContainer.Children.Add(platformGrid);
                    }
                }
                else
                {
                    _logger.LogWarning("SpotPlatformsContainer控件未找到");
                }

                _logger.LogDebug($"已更新{platformsData.Length}个现货平台的显示");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新现货平台显示时发生错误");
            }
        }

        /// <summary>
        /// 创建现货平台行UI
        /// </summary>
        private Grid CreateSpotPlatformRow(SpotPlatformData platform)
        {
            var grid = new Grid { Margin = new Thickness(0, 0, 0, 8) };

            // 定义列
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1.2, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1.2, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1.5, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1.3, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1.5, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1.3, GridUnitType.Star) });

            // 平台名
            var nameText = new TextBlock
            {
                Text = platform.PlatformName,
                FontSize = 11,
                FontWeight = FontWeights.SemiBold,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(nameText, 0);
            grid.Children.Add(nameText);

            // 买入价
            var buyPriceText = new TextBlock
            {
                Text = platform.BuyPrice.ToString("F2"),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = GetTrendBrush(platform.Trend, true),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(buyPriceText, 1);
            grid.Children.Add(buyPriceText);

            // 卖出价
            var sellPriceText = new TextBlock
            {
                Text = platform.SellPrice.ToString("F2"),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = GetTrendBrush(platform.Trend, false),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(sellPriceText, 2);
            grid.Children.Add(sellPriceText);

            // 计算基差（需要期货价格）
            if (_lastFuturesPriceData != null)
            {
                // 正向基差 = 现货买入价 - 期货卖出价
                var forwardBasis = platform.BuyPrice - _lastFuturesPriceData.SellPrice;
                var forwardBasisPanel = CreateBasisPanel(forwardBasis);
                Grid.SetColumn(forwardBasisPanel, 3);
                grid.Children.Add(forwardBasisPanel);

                // 正向开仓按钮
                var forwardButton = new Button
                {
                    Content = "正向开仓",
                    FontSize = 11,
                    Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    Padding = new Thickness(8, 4, 8, 4),
                    Margin = new Thickness(2)
                };
                Grid.SetColumn(forwardButton, 4);
                grid.Children.Add(forwardButton);

                // 反向基差 = 现货卖出价 - 期货买入价
                var reverseBasis = platform.SellPrice - _lastFuturesPriceData.BuyPrice;
                var reverseBasisPanel = CreateBasisPanel(reverseBasis);
                Grid.SetColumn(reverseBasisPanel, 5);
                grid.Children.Add(reverseBasisPanel);

                // 反向开仓按钮
                var reverseButton = new Button
                {
                    Content = "反向开仓",
                    FontSize = 11,
                    Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    Padding = new Thickness(8, 4, 8, 4),
                    Margin = new Thickness(2)
                };
                Grid.SetColumn(reverseButton, 6);
                grid.Children.Add(reverseButton);
            }

            return grid;
        }

        /// <summary>
        /// 创建基差显示面板
        /// </summary>
        private StackPanel CreateBasisPanel(decimal basisValue)
        {
            var panel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var basisText = new TextBlock
            {
                Text = basisValue.ToString("F2"),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = basisValue >= 0 ? new SolidColorBrush(Color.FromRgb(40, 167, 69)) : new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            var historyText = new TextBlock
            {
                Text = $"{basisValue:F2}/{(basisValue * 1.2m):F2}",
                FontSize = 9,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            panel.Children.Add(basisText);
            panel.Children.Add(historyText);

            return panel;
        }

        /// <summary>
        /// 根据趋势获取颜色画刷 - 上涨红色，下跌绿色，稳定时保持默认颜色
        /// </summary>
        private Brush GetTrendBrush(PriceTrend trend, bool isBuyPrice)
        {
            return trend switch
            {
                PriceTrend.Up => new SolidColorBrush(Color.FromRgb(231, 76, 60)),    // 上涨红色
                PriceTrend.Down => new SolidColorBrush(Color.FromRgb(39, 174, 96)),  // 下跌绿色
                _ => new SolidColorBrush(Color.FromRgb(73, 80, 87))                  // 稳定时使用默认颜色，不使用灰色
            };
        }

        /// <summary>
        /// 更新持仓订单数据显示
        /// </summary>
        private async Task UpdatePositionOrdersDisplay(PositionOrderData[] positionOrders)
        {
            try
            {
                // 使用PositionOrdersManagerViewModel的LoadPositionOrdersAsync方法来正确加载数据
                if (_positionOrdersManagerViewModel != null)
                {
                    _logger.LogInformation($"🔄 通过PositionOrdersManagerViewModel更新{positionOrders.Length}个持仓订单");

                    // 使用PositionOrdersManagerViewModel的方法来加载数据，这样可以确保消息接收和价格初始化正常工作
                    await _positionOrdersManagerViewModel.LoadPositionOrdersAsync();

                    _logger.LogInformation($"✅ PositionOrdersManagerViewModel数据加载完成，当前有{_positionOrdersManagerViewModel.PositionOrders.Count}个订单");

                    // 通知UI更新 - 由于使用了数据绑定，UI会自动更新
                    _logger.LogInformation("📱 UI将通过数据绑定自动更新持仓订单显示");
                }
                else
                {
                    _logger.LogWarning("PositionOrdersManagerViewModel未初始化");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新持仓订单显示时发生错误");
            }
        }

        /// <summary>
        /// 处理删除持仓订单
        /// </summary>
        private async Task HandleDeletePositionOrder(string orderId)
        {
            try
            {
                _logger.LogInformation("处理删除持仓订单请求: {OrderId}", orderId);

                if (_positionOrdersManagerViewModel != null)
                {
                    var success = await _positionOrdersManagerViewModel.DeletePositionOrderAsync(orderId);
                    if (success)
                    {
                        _logger.LogInformation("✅ 持仓订单删除成功: {OrderId}", orderId);

                        // 显示成功消息
                        MessageBox.Show($"持仓订单 {orderId} 已删除", "删除成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 刷新持仓订单列表
                        await RefreshPositionOrdersAsync();
                    }
                    else
                    {
                        _logger.LogError("❌ 持仓订单删除失败: {OrderId}", orderId);
                        MessageBox.Show("删除持仓订单时发生错误", "删除失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理删除持仓订单时发生异常: {OrderId}", orderId);
                MessageBox.Show($"删除持仓订单时发生异常: {ex.Message}", "删除失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理平仓订单（已弃用 - 现在使用新的两段式平仓流程）
        /// </summary>
        private async Task HandleClosePositionOrder(string orderId)
        {
            try
            {
                _logger.LogInformation("⚠️ 收到旧的平仓订单请求，已弃用: {OrderId}", orderId);

                // 旧的平仓逻辑已被新的两段式平仓流程替代
                // 新流程直接在 PositionOrderCard 中启动，无需通过消息传递
                _logger.LogWarning("旧的平仓流程已被弃用，请使用新的两段式平仓流程");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理旧平仓订单消息时发生异常: {OrderId}", orderId);
            }
        }

        /// <summary>
        /// 刷新持仓订单列表
        /// </summary>
        private async Task RefreshPositionOrdersAsync()
        {
            try
            {
                if (_positionOrdersManagerViewModel != null)
                {
                    await _positionOrdersManagerViewModel.LoadPositionOrdersAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新持仓订单列表时发生错误");
            }
        }



        /// <summary>
        /// 加载历史订单数据
        /// </summary>
        private async Task LoadHistoricalOrdersAsync()
        {
            try
            {
                if (_orderApiService == null)
                {
                    _logger.LogWarning("OrderApiService未初始化，无法加载历史订单");
                    return;
                }

                _logger.LogInformation("📜 开始加载历史订单数据...");

                // 获取历史订单数据
                var response = await _orderApiService.GetHistoryOrdersAsync(1, 1, 50); // 用户ID=1，第1页，50条记录
                if (response.Success && response.Data != null)
                {
                    // 更新UI线程中的历史订单集合
                    await Dispatcher.InvokeAsync(() =>
                    {
                        HistoricalOrders.Clear();
                        foreach (var order in response.Data.Orders)
                        {
                            HistoricalOrders.Add(order);
                        }
                    });

                    _logger.LogInformation("✅ 历史订单数据加载成功");

                    // 更新历史订单UI显示
                    await UpdateHistoricalOrdersDisplayAsync();
                }
                else
                {
                    _logger.LogWarning("❌ 历史订单数据加载失败: {Message}", response.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 加载历史订单数据时发生异常");
                throw;
            }
        }

        /// <summary>
        /// 更新历史订单UI显示
        /// </summary>
        private async Task UpdateHistoricalOrdersDisplayAsync()
        {
            try
            {
                await Dispatcher.InvokeAsync(() =>
                {
                    // 清空现有的历史订单显示
                    HistoryOrdersContainer.Children.Clear();

                    // 为每个历史订单创建UI行
                    foreach (var order in HistoricalOrders)
                    {
                        var orderRow = CreateHistoricalOrderRow(order);
                        HistoryOrdersContainer.Children.Add(orderRow);
                    }
                });

                _logger.LogInformation("✅ 历史订单UI显示更新完成，共 {Count} 条记录", HistoricalOrders.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 更新历史订单UI显示时发生错误");
            }
        }

        /// <summary>
        /// 创建历史订单行UI元素
        /// </summary>
        private Border CreateHistoricalOrderRow(HistoricalOrderData order)
        {
            try
            {
                // 创建主容器
                var border = new Border
                {
                    Background = new SolidColorBrush(Colors.White),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(241, 243, 244)),
                    BorderThickness = new Thickness(0, 0, 0, 1),
                    Margin = new Thickness(0, 0, 0, 0),
                    MinHeight = 28
                };

                // 添加鼠标悬停效果
                var style = new Style(typeof(Border));
                var trigger = new Trigger { Property = Border.IsMouseOverProperty, Value = true };
                trigger.Setters.Add(new Setter(Border.BackgroundProperty, new SolidColorBrush(Color.FromRgb(248, 249, 250))));
                style.Triggers.Add(trigger);
                border.Style = style;

                // 创建网格布局
                var grid = new Grid { Margin = new Thickness(0, 2, 0, 0) };

                // 定义列
                var columnDefinitions = new[]
                {
                    new ColumnDefinition { Width = new GridLength(0.8, GridUnitType.Star) }, // 订单ID
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 现货平台
                    new ColumnDefinition { Width = new GridLength(0.8, GridUnitType.Star) }, // 开仓时间
                    new ColumnDefinition { Width = new GridLength(0.8, GridUnitType.Star) }, // 平仓时间
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 持仓时长
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 开仓现货
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 开仓期货
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 开仓基差
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 平仓现货
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 平仓期货
                    new ColumnDefinition { Width = new GridLength(0.6, GridUnitType.Star) }, // 平仓基差
                    new ColumnDefinition { Width = new GridLength(0.7, GridUnitType.Star) }, // 现货盈亏
                    new ColumnDefinition { Width = new GridLength(0.7, GridUnitType.Star) }, // 期货盈亏
                    new ColumnDefinition { Width = new GridLength(0.5, GridUnitType.Star) }, // 总成本
                    new ColumnDefinition { Width = new GridLength(0.7, GridUnitType.Star) }  // 净利润
                };

                foreach (var colDef in columnDefinitions)
                {
                    grid.ColumnDefinitions.Add(colDef);
                }

                // 添加盈亏状态指示器
                var isProfit = order.NetProfit >= 0;
                var indicator = new Rectangle
                {
                    Fill = Brushes.Transparent,
                    Stroke = new SolidColorBrush(isProfit ? Color.FromRgb(40, 167, 69) : Color.FromRgb(220, 53, 69)),
                    StrokeThickness = 3,
                    HorizontalAlignment = HorizontalAlignment.Left,
                    Width = 3
                };
                Grid.SetColumnSpan(indicator, 15);
                grid.Children.Add(indicator);

                // 创建数据文本块
                var textBlocks = new[]
                {
                    CreateTextBlock(order.Id, 0),
                    CreateTextBlock(GetPlatformDisplayName(order.PlatformId), 1),
                    CreateTextBlock(order.OpenTime.ToString("MM/dd HH:mm"), 2),
                    CreateTextBlock(order.CloseTime.ToString("MM/dd HH:mm"), 3),
                    CreateTextBlock(FormatHoldingTime(order.HoldingHours), 4),
                    CreateTextBlock(order.SpotOpenPrice.ToString("F2"), 5),
                    CreateTextBlock(order.FutureOpenPrice.ToString("F2"), 6),
                    CreateTextBlock(order.OpenBasis.ToString("F2"), 7),
                    CreateTextBlock(order.SpotClosePrice.ToString("F2"), 8),
                    CreateTextBlock(order.FutureClosePrice.ToString("F2"), 9),
                    CreateTextBlock(order.CloseBasis.ToString("F2"), 10),
                    CreatePnlTextBlock(order.SpotPnl, 11),
                    CreatePnlTextBlock(order.FuturePnl, 12),
                    CreateTextBlock(order.TotalCost.ToString("F0"), 13),
                    CreatePnlTextBlock(order.NetProfit, 14)
                };

                foreach (var textBlock in textBlocks)
                {
                    grid.Children.Add(textBlock);
                }

                border.Child = grid;
                return border;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建历史订单行时发生错误: {OrderId}", order.Id);

                // 返回错误提示行
                var errorBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(255, 243, 205)),
                    Padding = new Thickness(16, 8, 16, 8),
                    Margin = new Thickness(0, 2, 0, 0)
                };

                var errorText = new TextBlock
                {
                    Text = $"加载订单 {order.Id} 时发生错误",
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(133, 100, 4)),
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                errorBorder.Child = errorText;
                return errorBorder;
            }
        }

        /// <summary>
        /// 创建普通文本块
        /// </summary>
        private TextBlock CreateTextBlock(string text, int column)
        {
            var textBlock = new TextBlock
            {
                Text = text,
                FontSize = 10,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80)),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Padding = new Thickness(1),
                FontFamily = column == 0 ? new FontFamily("Courier New") : new FontFamily("Segoe UI")
            };

            Grid.SetColumn(textBlock, column);
            return textBlock;
        }

        /// <summary>
        /// 创建盈亏文本块（带颜色）
        /// </summary>
        private TextBlock CreatePnlTextBlock(decimal value, int column)
        {
            var textBlock = new TextBlock
            {
                Text = value >= 0 ? $"+{value:F0}" : value.ToString("F0"),
                FontSize = 10,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(value >= 0 ? Color.FromRgb(40, 167, 69) : Color.FromRgb(220, 53, 69)),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Padding = new Thickness(1)
            };

            Grid.SetColumn(textBlock, column);
            return textBlock;
        }

        /// <summary>
        /// 获取平台显示名称
        /// </summary>
        private string GetPlatformDisplayName(string platformId)
        {
            return platformId?.ToLower() switch
            {
                "chuangfu" => "创富",
                "fupai" => "福牌",
                "rencheng" => "仁成",
                "yinghuida" => "盈汇达",
                _ => platformId ?? "未知"
            };
        }

        /// <summary>
        /// 格式化持仓时长
        /// </summary>
        private string FormatHoldingTime(decimal hours)
        {
            var totalMinutes = (int)(hours * 60);
            var h = totalMinutes / 60;
            var m = totalMinutes % 60;
            return $"{h}h{m:D2}m";
        }

        /// <summary>
        /// 递归查找子控件
        /// </summary>
        private DependencyObject? FindChildByName(DependencyObject parent, string name)
        {
            if (parent == null) return null;

            // 检查当前对象
            if (parent is FrameworkElement element && element.Name == name)
            {
                return parent;
            }

            // 递归检查子对象
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                var result = FindChildByName(child, name);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }

        /// <summary>
        /// 创建持仓订单卡片UI
        /// </summary>
        private Border CreatePositionOrderCard(PositionOrderData position)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 16),
                MinHeight = 200,
                BorderBrush = position.Direction == 1 ?
                    new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                    new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                BorderThickness = new Thickness(3, 0, 0, 0),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Color.FromArgb(25, 0, 0, 0),
                    Direction = 270,
                    ShadowDepth = 2,
                    BlurRadius = 8,
                    Opacity = 0.15
                }
            };

            var mainPanel = new StackPanel();

            // 订单头部
            var headerGrid = new Grid { Margin = new Thickness(0, 0, 0, 12) };
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var leftPanel = new StackPanel();
            var orderIdPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 4) };

            var orderIdText = new TextBlock
            {
                Text = position.Id,
                FontSize = 11,
                FontFamily = new FontFamily("Segoe UI"),
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            orderIdPanel.Children.Add(orderIdText);

            var directionBorder = new Border
            {
                Background = position.Direction == 1 ?
                    new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                    new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(8, 2, 8, 2),
                Margin = new Thickness(8, 0, 0, 0)
            };
            var directionText = new TextBlock
            {
                Text = position.Direction == 1 ? "正向" : "反向",
                FontSize = 9,
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold
            };
            directionBorder.Child = directionText;
            orderIdPanel.Children.Add(directionBorder);

            leftPanel.Children.Add(orderIdPanel);

            var openTimeText = new TextBlock
            {
                Text = $"开仓: {position.OpenTime:yyyy/MM/dd HH:mm:ss}",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Margin = new Thickness(0, 0, 0, 3)
            };
            leftPanel.Children.Add(openTimeText);

            // 订单号容器
            var orderNumbersPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 0) };

            // 现货订单号
            var spotOrderPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 2) };
            var spotLabel = new TextBlock
            {
                Text = "现货:",
                FontSize = 9,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87)),
                Width = 35
            };
            var spotOrderText = new TextBlock
            {
                Text = position.SpotOrderId,
                FontSize = 9,
                FontFamily = new FontFamily("Consolas"),
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };
            spotOrderPanel.Children.Add(spotLabel);
            spotOrderPanel.Children.Add(spotOrderText);
            orderNumbersPanel.Children.Add(spotOrderPanel);

            // 期货订单号
            var futureOrderPanel = new StackPanel { Orientation = Orientation.Horizontal };
            var futureLabel = new TextBlock
            {
                Text = "期货:",
                FontSize = 9,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87)),
                Width = 35
            };
            var futureOrderText = new TextBlock
            {
                Text = position.FutureOrderId,
                FontSize = 9,
                FontFamily = new FontFamily("Consolas"),
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };
            futureOrderPanel.Children.Add(futureLabel);
            futureOrderPanel.Children.Add(futureOrderText);
            orderNumbersPanel.Children.Add(futureOrderPanel);

            leftPanel.Children.Add(orderNumbersPanel);

            Grid.SetColumn(leftPanel, 0);
            headerGrid.Children.Add(leftPanel);

            var deleteButtonBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                CornerRadius = new CornerRadius(6),
                Cursor = Cursors.Hand
            };

            var deleteButton = new Button
            {
                Content = "删除",
                FontSize = 10,
                FontWeight = FontWeights.Medium,
                Padding = new Thickness(12, 6, 12, 6),
                Background = Brushes.Transparent,
                Foreground = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand
            };

            // 添加删除按钮点击事件处理
            deleteButton.Click += async (sender, e) =>
            {
                await HandleDeletePositionOrder(position.InternalOrderId);
            };

            deleteButtonBorder.Child = deleteButton;
            Grid.SetColumn(deleteButtonBorder, 1);
            headerGrid.Children.Add(deleteButtonBorder);

            mainPanel.Children.Add(headerGrid);

            // 价格对比
            var priceGrid = new Grid { Margin = new Thickness(0, 8, 0, 12) };
            priceGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            priceGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 开仓价格
            var openPricePanel = CreatePricePanel("开仓价格", position.SpotOpenPrice, position.FutureOpenPrice, position.OpenBasis);
            openPricePanel.Margin = new Thickness(0, 0, 8, 0);
            Grid.SetColumn(openPricePanel, 0);
            priceGrid.Children.Add(openPricePanel);

            // 当前价格
            var currentPricePanel = CreatePricePanel("当前价格", position.CurrentSpotPrice, position.CurrentFuturePrice, position.CurrentBasis);
            currentPricePanel.Margin = new Thickness(8, 0, 0, 0);
            Grid.SetColumn(currentPricePanel, 1);
            priceGrid.Children.Add(currentPricePanel);

            mainPanel.Children.Add(priceGrid);

            // 盈亏信息
            var pnlPanel = CreatePnlPanel(position);
            mainPanel.Children.Add(pnlPanel);

            card.Child = mainPanel;
            return card;
        }





        /// <summary>
        /// 创建价格面板
        /// </summary>
        private StackPanel CreatePricePanel(string title, decimal spotPrice, decimal futurePrice, decimal basis)
        {
            var panel = new StackPanel
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                Margin = new Thickness(0),
            };

            // 添加圆角边框
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(12, 10, 12, 10)
            };

            var innerPanel = new StackPanel();

            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 11,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64)),
                Margin = new Thickness(0, 0, 0, 6),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            innerPanel.Children.Add(titleText);

            var pricesPanel = new StackPanel();

            // 现货价格
            var spotGrid = new Grid { Margin = new Thickness(0, 0, 0, 4) };
            spotGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            spotGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var spotLabel = new TextBlock
            {
                Text = "现货:",
                FontSize = 10,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87))
            };
            Grid.SetColumn(spotLabel, 0);
            spotGrid.Children.Add(spotLabel);

            var spotValue = new TextBlock
            {
                Text = spotPrice.ToString("F2"),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Right,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            Grid.SetColumn(spotValue, 1);
            spotGrid.Children.Add(spotValue);

            pricesPanel.Children.Add(spotGrid);

            // 期货价格
            var futureGrid = new Grid { Margin = new Thickness(0, 0, 0, 4) };
            futureGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            futureGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var futureLabel = new TextBlock
            {
                Text = "期货:",
                FontSize = 10,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87))
            };
            Grid.SetColumn(futureLabel, 0);
            futureGrid.Children.Add(futureLabel);

            var futureValue = new TextBlock
            {
                Text = futurePrice.ToString("F2"),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Right,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            Grid.SetColumn(futureValue, 1);
            futureGrid.Children.Add(futureValue);

            pricesPanel.Children.Add(futureGrid);

            // 基差
            var basisGrid = new Grid();
            basisGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            basisGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var basisLabel = new TextBlock
            {
                Text = "基差:",
                FontSize = 10,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87))
            };
            Grid.SetColumn(basisLabel, 0);
            basisGrid.Children.Add(basisLabel);

            var basisValue = new TextBlock
            {
                Text = (basis >= 0 ? "+" : "") + basis.ToString("F2"),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Right,
                Foreground = basis >= 0 ? new SolidColorBrush(Color.FromRgb(40, 167, 69)) : new SolidColorBrush(Color.FromRgb(220, 53, 69))
            };
            Grid.SetColumn(basisValue, 1);
            basisGrid.Children.Add(basisValue);

            pricesPanel.Children.Add(basisGrid);

            innerPanel.Children.Add(pricesPanel);
            border.Child = innerPanel;
            panel.Children.Add(border);
            return panel;
        }

        /// <summary>
        /// 创建盈亏面板
        /// </summary>
        private StackPanel CreatePnlPanel(PositionOrderData position)
        {
            var panel = new StackPanel();

            // 第一行：现货盈亏、期货盈亏、总盈亏、开仓克重
            var firstRowGrid = new Grid { Margin = new Thickness(0, 0, 0, 16) };
            for (int i = 0; i < 4; i++)
            {
                firstRowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            }

            // 现货盈亏
            var spotPnlPanel = CreatePnlItem("现货盈亏", position.SpotPnl);
            Grid.SetColumn(spotPnlPanel, 0);
            firstRowGrid.Children.Add(spotPnlPanel);

            // 期货盈亏
            var futurePnlPanel = CreatePnlItem("期货盈亏", position.FuturePnl);
            Grid.SetColumn(futurePnlPanel, 1);
            firstRowGrid.Children.Add(futurePnlPanel);

            // 总盈亏
            var totalPnlPanel = CreatePnlItem("总盈亏", position.TotalPnl);
            Grid.SetColumn(totalPnlPanel, 2);
            firstRowGrid.Children.Add(totalPnlPanel);

            // 开仓克重
            var openWeightPanel = CreatePnlItem("开仓克重", position.OpenWeight, true, "g");
            Grid.SetColumn(openWeightPanel, 3);
            firstRowGrid.Children.Add(openWeightPanel);

            panel.Children.Add(firstRowGrid);

            // 分割线
            var separator = new Border
            {
                Height = 1,
                Background = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                Margin = new Thickness(0, 12, 0, 16),
                CornerRadius = new CornerRadius(0.5)
            };
            panel.Children.Add(separator);

            // 第二行：总成本、净利润
            var secondRowGrid = new Grid();
            for (int i = 0; i < 2; i++)
            {
                secondRowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            }

            // 计算总成本：(现货成本 + 期货成本) * 开仓克重
            // 从交易配置中获取成本费率
            decimal spotCostRate = 0.40m; // 现货成本 0.40元/克
            decimal futureCostRate = 0.04m; // 期货成本 0.04元/克
            decimal calculatedTotalCost = (spotCostRate + futureCostRate) * position.OpenWeight;

            // 总成本
            var totalCostPanel = CreatePnlItem("总成本", calculatedTotalCost, true);
            Grid.SetColumn(totalCostPanel, 0);
            secondRowGrid.Children.Add(totalCostPanel);

            // 净利润
            var netProfitPanel = CreatePnlItem("净利润", position.NetProfit);
            Grid.SetColumn(netProfitPanel, 1);
            secondRowGrid.Children.Add(netProfitPanel);

            panel.Children.Add(secondRowGrid);

            // 第三行：平仓按钮
            var buttonGrid = new Grid { Margin = new Thickness(0, 16, 0, 0) };

            var closeButton = new Button
            {
                Content = "平仓",
                FontSize = 13,
                FontWeight = FontWeights.SemiBold,
                Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(24, 12, 24, 12),
                Margin = new Thickness(0),
                Cursor = Cursors.Hand,
                MinWidth = 120,
                Height = 40,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            buttonGrid.Children.Add(closeButton);
            panel.Children.Add(buttonGrid);

            return panel;
        }

        /// <summary>
        /// 创建盈亏项目
        /// </summary>
        private StackPanel CreatePnlItem(string label, decimal value, bool isCost = false, string unit = "")
        {
            var panel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };

            var labelText = new TextBlock
            {
                Text = label,
                FontSize = 10,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 4)
            };
            panel.Children.Add(labelText);

            var valueText = new TextBlock
            {
                Text = isCost ? value.ToString("F0") + unit : (value >= 0 ? "+" : "") + value.ToString("F2"),
                FontSize = 13,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            if (isCost)
            {
                valueText.Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87));
            }
            else
            {
                valueText.Foreground = value >= 0 ?
                    new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                    new SolidColorBrush(Color.FromRgb(220, 53, 69));
            }

            panel.Children.Add(valueText);
            return panel;
        }

        /// <summary>
        /// 更新订单统计数据显示
        /// </summary>
        private void UpdateOrderStatisticsDisplay(OrderStatisticsSummary stats)
        {
            try
            {
                // 更新今日净利润
                var todayProfitValue = FindName("TodayProfitValue") as TextBlock;
                if (todayProfitValue != null)
                {
                    todayProfitValue.Text = stats.TodayProfit >= 0 ? $"+{stats.TodayProfit:F2}" : stats.TodayProfit.ToString("F2");
                    todayProfitValue.Foreground = stats.TodayProfit >= 0 ?
                        new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                        new SolidColorBrush(Color.FromRgb(220, 53, 69));
                }

                // 更新七日净利润
                var sevenDayProfitValue = FindName("SevenDayProfitValue") as TextBlock;
                if (sevenDayProfitValue != null)
                {
                    sevenDayProfitValue.Text = stats.SevenDayProfit >= 0 ? $"+{stats.SevenDayProfit:F2}" : stats.SevenDayProfit.ToString("F2");
                    sevenDayProfitValue.Foreground = stats.SevenDayProfit >= 0 ?
                        new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                        new SolidColorBrush(Color.FromRgb(220, 53, 69));
                }

                // 更新30日净利润
                var thirtyDayProfitValue = FindName("ThirtyDayProfitValue") as TextBlock;
                if (thirtyDayProfitValue != null)
                {
                    thirtyDayProfitValue.Text = stats.ThirtyDayProfit >= 0 ? $"+{stats.ThirtyDayProfit:F2}" : stats.ThirtyDayProfit.ToString("F2");
                    thirtyDayProfitValue.Foreground = stats.ThirtyDayProfit >= 0 ?
                        new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                        new SolidColorBrush(Color.FromRgb(220, 53, 69));
                }

                // 更新本月净利润
                var monthProfitValue = FindName("MonthProfitValue") as TextBlock;
                if (monthProfitValue != null)
                {
                    monthProfitValue.Text = stats.MonthProfit >= 0 ? $"+{stats.MonthProfit:F2}" : stats.MonthProfit.ToString("F2");
                    monthProfitValue.Foreground = stats.MonthProfit >= 0 ?
                        new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                        new SolidColorBrush(Color.FromRgb(220, 53, 69));
                }

                // 更新今日开仓数
                var todayOpenCountValue = FindName("TodayOpenCountValue") as TextBlock;
                if (todayOpenCountValue != null)
                {
                    todayOpenCountValue.Text = stats.TodayOpenCount.ToString();
                }

                // 更新今日平仓数
                var todayCloseCountValue = FindName("TodayCloseCountValue") as TextBlock;
                if (todayCloseCountValue != null)
                {
                    todayCloseCountValue.Text = stats.TodayCloseCount.ToString();
                }

                // 更新胜率
                var winRateValue = FindName("WinRateValue") as TextBlock;
                if (winRateValue != null)
                {
                    winRateValue.Text = $"{stats.WinRate:F1}%";
                    winRateValue.Foreground = stats.WinRate >= 60 ?
                        new SolidColorBrush(Color.FromRgb(40, 167, 69)) :
                        new SolidColorBrush(Color.FromRgb(220, 53, 69));
                }

                // 更新平均持仓时长
                var averageHoldingTimeValue = FindName("AverageHoldingTimeValue") as TextBlock;
                if (averageHoldingTimeValue != null)
                {
                    var hours = (int)stats.AverageHoldingHours;
                    var minutes = (int)((stats.AverageHoldingHours - hours) * 60);
                    averageHoldingTimeValue.Text = $"{hours}h{minutes:D2}m";
                }

                _logger.LogDebug("交易统计数据显示已更新");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新交易统计数据显示时发生错误");
            }
        }

        /// <summary>
        /// 更新历史订单数据显示
        /// </summary>
        private void UpdateHistoricalOrdersDisplay(HistoricalOrderData[] orders)
        {
            try
            {
                // 清空现有的历史订单容器
                var historyOrdersContainer = FindName("HistoryOrdersContainer") as Panel;
                if (historyOrdersContainer != null)
                {
                    historyOrdersContainer.Children.Clear();

                    // 为每个历史订单创建UI元素
                    foreach (var order in orders)
                    {
                        var orderRow = CreateHistoricalOrderRow(order);
                        historyOrdersContainer.Children.Add(orderRow);
                    }
                }
                else
                {
                    _logger.LogWarning("HistoryOrdersContainer控件未找到");
                }

                _logger.LogDebug($"已更新{orders.Length}个历史订单的显示");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新历史订单显示时发生错误");
            }
        }



        /// <summary>
        /// 获取简短的订单ID显示
        /// </summary>
        private string GetShortOrderId(string fullOrderId)
        {
            if (string.IsNullOrEmpty(fullOrderId))
                return "";

            // 如果订单ID以ORD开头，只显示后面的数字部分的最后8位
            if (fullOrderId.StartsWith("ORD") && fullOrderId.Length > 11)
            {
                var numberPart = fullOrderId.Substring(3); // 去掉ORD前缀
                if (numberPart.Length > 8)
                {
                    return "..." + numberPart.Substring(numberPart.Length - 8);
                }
                return numberPart;
            }

            // 如果不是标准格式，显示前8个字符
            return fullOrderId.Length > 8 ? fullOrderId.Substring(0, 8) + "..." : fullOrderId;
        }




        /// <summary>
        /// 处理现货平台勾选状态变更事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">路由事件参数</param>
        private async void OnPlatformCheckboxChanged(object sender, RoutedEventArgs e)
        {
            // 在构造函数完成之前，服务可能还没有初始化，直接返回
            if (_userConfigService == null || _logger == null)
            {
                return;
            }

            // 如果正在初始化CheckBox状态，忽略事件以避免重复触发配置变更
            if (_isInitializingCheckBoxes)
            {
                return;
            }

            if (sender is not CheckBox checkBox || checkBox.Tag is not string platformId)
            {
                _logger.LogWarning("OnPlatformCheckboxChanged: 无效的发送者或Tag");
                return;
            }

            bool isEnabled = checkBox.IsChecked ?? false;

            try
            {
                _logger.LogInformation("用户{Action}平台 {PlatformId}",
                    isEnabled ? "启用" : "禁用", platformId);

                // 禁用CheckBox防止重复点击
                checkBox.IsEnabled = false;

                // 显示处理中状态
                ShowOperationMessage("⏳", "正在更新平台配置...", "#17a2b8", "#d1ecf1");

                // 更新用户配置
                bool success = await _userConfigService.UpdatePlatformEnabledAsync(1, platformId, isEnabled);

                if (success)
                {
                    // 更新状态显示
                    await UpdatePlatformStatusDisplay(platformId, isEnabled);
                    await UpdatePlatformCount();
                    ShowOperationMessage("✅", $"平台{(isEnabled ? "启用" : "禁用")}成功", "#28a745", "#d4edda");

                    // 快速刷新现货平台配置
                    if (SpotPlatformMarket != null)
                    {
                        await SpotPlatformMarket.RefreshPlatformConfigAsync();
                    }

                    // 重启WebSocket服务以应用单个平台的配置变更
                    await RestartWebSocketServiceWithNewConfig();

                    _logger.LogInformation("平台 {PlatformId} 状态已成功更新为: {Status}，WebSocket服务已重启",
                        platformId, isEnabled ? "启用" : "禁用");

                    // 3秒后隐藏提示
                    HideOperationMessageAfterDelay(3000);
                }
                else
                {
                    _logger.LogError("更新平台 {PlatformId} 状态失败", platformId);

                    // 回滚CheckBox状态
                    checkBox.IsChecked = !isEnabled;
                    ShowOperationMessage("❌", "更新平台状态失败，请重试", "#dc3545", "#f8d7da");
                    HideOperationMessageAfterDelay(5000);
                }
            }
            catch (Exception ex)
            {
                // 添加null检查，防止logger未初始化导致的异常
                _logger?.LogError(ex, "处理平台 {PlatformId} 状态变更时发生错误", platformId);

                // 回滚CheckBox状态
                checkBox.IsChecked = !isEnabled;
                ShowOperationMessage("❌", $"操作失败: {ex.Message}", "#dc3545", "#f8d7da");
                HideOperationMessageAfterDelay(5000);
            }
            finally
            {
                // 重新启用CheckBox
                checkBox.IsEnabled = true;
            }
        }

        /// <summary>
        /// 更新平台状态显示
        /// </summary>
        /// <param name="platformId">平台ID</param>
        /// <param name="isEnabled">是否启用</param>
        private async Task UpdatePlatformStatusDisplay(string platformId, bool isEnabled)
        {
            try
            {
                // 确保在UI线程上执行
                if (!Dispatcher.CheckAccess())
                {
                    await Dispatcher.InvokeAsync(() => UpdatePlatformStatusDisplaySync(platformId, isEnabled));
                    return;
                }

                UpdatePlatformStatusDisplaySync(platformId, isEnabled);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新平台 {PlatformId} 状态显示时发生错误", platformId);
            }
        }

        /// <summary>
        /// 同步更新平台状态显示（UI线程）
        /// </summary>
        /// <param name="platformId">平台ID</param>
        /// <param name="isEnabled">是否启用</param>
        private void UpdatePlatformStatusDisplaySync(string platformId, bool isEnabled)
        {
            try
            {
                // 查找对应的状态显示控件并更新
                var statusBorder = FindPlatformStatusBorderSync(platformId);
                if (statusBorder != null)
                {
                    // 更新背景颜色
                    statusBorder.Background = new SolidColorBrush(isEnabled ?
                        Color.FromRgb(40, 167, 69) :   // 绿色 - 启用
                        Color.FromRgb(108, 117, 125));  // 灰色 - 禁用

                    // 更新状态文本
                    if (statusBorder.Child is TextBlock statusText)
                    {
                        statusText.Text = isEnabled ? "已启用" : "未启用";
                    }

                    _logger.LogDebug("已更新平台 {PlatformId} 的状态显示", platformId);
                }
                else
                {
                    _logger.LogWarning("未找到平台 {PlatformId} 的状态显示控件", platformId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步更新平台 {PlatformId} 状态显示时发生错误", platformId);
            }
        }

        /// <summary>
        /// 保存平台配置按钮点击事件
        /// </summary>
        private async void SavePlatformConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("用户点击保存平台配置按钮");

                // 禁用保存按钮防止重复点击
                if (sender is Button saveButton)
                {
                    saveButton.IsEnabled = false;
                    saveButton.Content = "保存中...";
                }

                // 显示保存中状态
                ShowOperationMessage("⏳", "正在保存配置...", "#17a2b8", "#d1ecf1");

                // 获取所有平台的当前状态
                var platformStates = await GetAllPlatformStates();

                // 构建批量更新的字典
                var platformStatusDict = platformStates.ToDictionary(p => p.platformId, p => p.isEnabled);

                // 批量更新配置到数据库
                bool allSuccess = await _spotPlatformConfigService.UpdatePlatformEnabledStatusAsync(1, platformStatusDict);

                if (!allSuccess)
                {
                    _logger.LogError("批量保存平台配置失败");
                }

                if (allSuccess)
                {
                    // 批量更新所有状态显示（在UI线程上同步执行）
                    await Dispatcher.InvokeAsync(() =>
                    {
                        foreach (var (platformId, isEnabled) in platformStates)
                        {
                            UpdatePlatformStatusDisplaySync(platformId, isEnabled);
                        }
                    });

                    // 更新平台计数
                    await UpdatePlatformCount();

                    // 快速刷新现货平台配置（不重新加载价格数据）
                    if (SpotPlatformMarket != null)
                    {
                        await SpotPlatformMarket.RefreshPlatformConfigAsync();
                    }

                    // 重启WebSocket服务以应用新配置
                    await RestartWebSocketServiceWithNewConfig();

                    // 显示成功提示
                    ShowOperationMessage("✅", "配置保存成功，WebSocket服务已重启", "#28a745", "#d4edda");
                    _logger.LogInformation("所有平台配置保存成功，WebSocket服务已重启");
                }
                else
                {
                    // 显示部分失败提示
                    ShowOperationMessage("⚠️", "部分配置保存失败，请检查日志", "#ffc107", "#fff3cd");
                }

                // 3秒后隐藏提示
                HideOperationMessageAfterDelay(3000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存平台配置时发生错误");
                ShowOperationMessage("❌", $"保存失败: {ex.Message}", "#dc3545", "#f8d7da");
                HideOperationMessageAfterDelay(5000);
            }
            finally
            {
                // 恢复保存按钮状态
                if (sender is Button saveButton)
                {
                    saveButton.IsEnabled = true;
                    saveButton.Content = "保存平台配置";
                }
            }
        }

        /// <summary>
        /// 查找指定平台的状态显示Border控件（同步版本）
        /// </summary>
        /// <param name="platformId">平台ID</param>
        /// <returns>状态显示Border控件或null</returns>
        private Border? FindPlatformStatusBorderSync(string platformId)
        {
            try
            {
                // 直接映射平台ID到状态控件名称
                var platformToControlMap = new Dictionary<string, string>
                {
                    { "chuangfu", "ChuangfuStatus" },
                    { "rencheng", "RenchengStatus" },
                    { "fupai", "FupaiStatus" },
                    { "yinghuida", "YinghuidaStatus" }
                };

                if (platformToControlMap.TryGetValue(platformId, out string? statusControlName))
                {
                    return this.FindName(statusControlName) as Border;
                }

                _logger.LogWarning("未找到平台 {PlatformId} 的状态控件映射", platformId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找平台 {PlatformId} 状态控件时发生错误", platformId);
                return null;
            }
        }

        /// <summary>
        /// 获取所有平台的当前状态
        /// </summary>
        /// <returns>平台ID和启用状态的元组列表</returns>
        private async Task<List<(string platformId, bool isEnabled)>> GetAllPlatformStates()
        {
            var states = new List<(string, bool)>();

            try
            {
                // 直接映射平台ID到复选框控件名称
                var platformCheckboxMap = new Dictionary<string, string>
                {
                    { "chuangfu", "ChuangfuCheckbox" },
                    { "rencheng", "RenchengCheckbox" },
                    { "fupai", "FupaiCheckbox" },
                    { "yinghuida", "YinghuidaCheckbox" }
                };

                // 从数据库获取可用平台
                var availablePlatforms = await _spotPlatformConfigService.GetAvailablePlatformsAsync();

                foreach (var (platformId, checkboxName) in platformCheckboxMap)
                {
                    if (this.FindName(checkboxName) is CheckBox checkbox)
                    {
                        states.Add((platformId, checkbox.IsChecked ?? false));
                    }
                    else
                    {
                        _logger.LogWarning("未找到平台 {PlatformId} 的CheckBox控件: {CheckboxName}", platformId, checkboxName);
                        states.Add((platformId, false)); // 默认为未启用
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取平台状态时发生错误");
            }

            return states;
        }

        /// <summary>
        /// 更新平台计数显示
        /// </summary>
        private async Task UpdatePlatformCount()
        {
            try
            {
                var platformStates = await GetAllPlatformStates();
                var enabledCount = platformStates.Count(s => s.isEnabled);
                var totalCount = platformStates.Count;

                if (this.FindName("PlatformCountText") is TextBlock countText)
                {
                    countText.Text = $"已选择 {enabledCount} 个平台 (共 {totalCount} 个)";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新平台计数时发生错误");
            }
        }

        /// <summary>
        /// 显示操作提示消息
        /// </summary>
        /// <param name="icon">图标</param>
        /// <param name="message">消息内容</param>
        /// <param name="textColor">文字颜色</param>
        /// <param name="backgroundColor">背景颜色</param>
        private void ShowOperationMessage(string icon, string message, string textColor, string backgroundColor)
        {
            try
            {
                if (this.FindName("OperationMessageBorder") is Border messageBorder &&
                    this.FindName("OperationMessageIcon") is TextBlock iconText &&
                    this.FindName("OperationMessageText") is TextBlock messageText)
                {
                    // 设置图标和文本
                    iconText.Text = icon;
                    messageText.Text = message;
                    messageText.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(textColor));

                    // 设置背景和边框颜色
                    messageBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(backgroundColor));
                    messageBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(textColor));

                    // 显示消息
                    messageBorder.Visibility = Visibility.Visible;

                    _logger.LogDebug("显示操作提示: {Message}", message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示操作提示时发生错误");
            }
        }

        /// <summary>
        /// 延迟隐藏操作提示
        /// </summary>
        /// <param name="delayMs">延迟毫秒数</param>
        private async void HideOperationMessageAfterDelay(int delayMs)
        {
            try
            {
                await Task.Delay(delayMs);

                if (this.FindName("OperationMessageBorder") is Border messageBorder)
                {
                    messageBorder.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "隐藏操作提示时发生错误");
            }
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        private void ShowErrorMessage(string message)
        {
            // 使用新的操作提示方法
            ShowOperationMessage("❌", message, "#dc3545", "#f8d7da");
            HideOperationMessageAfterDelay(5000);
        }

        /// <summary>
        /// 处理交易执行消息
        /// </summary>
        /// <param name="message">交易执行消息</param>
        public void Receive(TradeExecutionMessage message)
        {
            try
            {
                _logger.LogInformation("收到交易执行消息: {TradeType} - {PlatformName}, 基差: {Basis}",
                    message.TradeType, message.PlatformName, message.Basis);

                // 在UI线程中显示交易执行提示
                Dispatcher.Invoke(() =>
                {
                    var tradeTypeText = message.TradeType == "Forward" ? "正向开仓" : "反向开仓";
                    var successMessage = $"🎯 {tradeTypeText}执行成功！\n" +
                                       $"平台：{message.PlatformName}\n" +
                                       $"价格：{message.SpotPrice:F2}\n" +
                                       $"基差：{message.Basis:F2}";

                    ShowOperationMessage("✅", successMessage, "#28a745", "#d4edda");
                    HideOperationMessageAfterDelay(5000);
                });

                // 这里可以添加更多的交易执行后处理逻辑
                // 例如：更新持仓信息、发送到交易服务等
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理交易执行消息时发生错误");
            }
        }

        /// <summary>
        /// 处理导航到标签页消息
        /// </summary>
        /// <param name="message">导航消息</param>
        public void Receive(NavigateToTabMessage message)
        {
            try
            {
                _logger.LogInformation("收到导航消息: 切换到标签页 {TabName}", message.TabName);

                Dispatcher.InvokeAsync(() =>
                {
                    SwitchToTab(message.TabName.ToLower());
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理导航消息时发生错误: TabName={TabName}", message.TabName);
            }
        }

        /// <summary>
        /// 重启WebSocket服务以应用新配置
        /// </summary>
        private async Task RestartWebSocketServiceWithNewConfig()
        {
            try
            {
                _logger.LogInformation("开始重启WebSocket服务以应用新的平台配置");

                // 停止现有的WebSocket连接
                if (_multiPlatformSpotPriceService != null)
                {
                    await _multiPlatformSpotPriceService.StopAllPlatformsAsync();
                    _logger.LogInformation("已停止所有现货平台WebSocket连接");
                }

                // 获取最新的用户配置
                var userConfig = await _userConfigService.GetUserConfigAsync(1);
                var enabledPlatforms = userConfig.EnabledPlatforms
                    .Where(kvp => kvp.Value)
                    .Select(kvp => kvp.Key)
                    .ToList();

                _logger.LogInformation("重启后将启用的平台: {EnabledPlatforms}", string.Join(", ", enabledPlatforms));

                // 根据新配置重新启动WebSocket连接
                if (enabledPlatforms.Any())
                {
                    await _multiPlatformSpotPriceService.StartEnabledPlatformsAsync(enabledPlatforms);
                    _logger.LogInformation("已根据新配置重新启动 {Count} 个平台的WebSocket连接", enabledPlatforms.Count);
                }
                else
                {
                    _logger.LogWarning("没有启用的平台，WebSocket服务保持停止状态");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重启WebSocket服务时发生错误");
            }
        }

        /// <summary>
        /// 刷新现货平台按钮点击事件
        /// </summary>
        private async void RefreshSpotPlatformsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("用户点击刷新现货平台按钮");

                // 显示刷新提示
                ShowOperationMessage("🔄", "正在刷新现货平台配置...", "#007bff", "#cce7ff");

                // 禁用刷新按钮防止重复点击
                if (sender is Button refreshButton)
                {
                    refreshButton.IsEnabled = false;
                }

                // 刷新现货平台配置
                if (SpotPlatformMarket != null)
                {
                    await SpotPlatformMarket.RefreshPlatformConfigAsync();
                    _logger.LogInformation("现货平台配置刷新完成，当前启用平台数: {Count}", SpotPlatformMarket.EnabledPlatformCount);

                    // 重启WebSocket服务以确保连接状态与配置一致
                    await RestartWebSocketServiceWithNewConfig();

                    // 显示成功提示
                    ShowOperationMessage("✅", $"刷新成功，已启用 {SpotPlatformMarket.EnabledPlatformCount} 个平台，WebSocket已重启", "#28a745", "#d4edda");
                }
                else
                {
                    _logger.LogWarning("SpotPlatformMarket 为 null，无法刷新");
                    ShowOperationMessage("⚠️", "刷新失败：现货平台服务未初始化", "#ffc107", "#fff3cd");
                }

                // 3秒后隐藏提示
                HideOperationMessageAfterDelay(3000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新现货平台配置时发生错误");
                ShowOperationMessage("❌", $"刷新失败: {ex.Message}", "#dc3545", "#f8d7da");
                HideOperationMessageAfterDelay(5000);
            }
            finally
            {
                // 重新启用刷新按钮
                if (sender is Button refreshButton)
                {
                    refreshButton.IsEnabled = true;
                }
            }
        }

        /// <summary>
        /// 处理合约选择器变更事件
        /// </summary>
        private async void OnContractSelectorChanged(object? sender, string newContract)
        {
            try
            {
                _logger.LogInformation($"合约选择器变更: {_selectedContract} -> {newContract}");

                if (_selectedContract == newContract)
                {
                    _logger.LogDebug("合约未发生变化，跳过处理");
                    return;
                }

                var oldContract = _selectedContract;
                _selectedContract = newContract;

                // 更新UI显示
                UpdateContractDisplay(newContract);

                // 通知期货价格显示ViewModel合约变更
                if (PriceDisplayViewModel != null)
                {
                    _logger.LogInformation("通知期货价格显示ViewModel合约变更");
                    // 更新期货合约显示
                    PriceDisplayViewModel.FuturesContract = GetContractDisplayName(newContract);
                }

                // 发送合约变更消息到其他组件
                var messenger = ((App)Application.Current).ServiceProvider?.GetService<IMessenger>();
                if (messenger != null)
                {
                    var contractChangeMessage = new ContractChangedMessage(newContract, oldContract);
                    messenger.Send(contractChangeMessage);
                    _logger.LogInformation("已发送合约变更消息");
                }

                _logger.LogInformation($"合约切换完成: {oldContract} -> {newContract}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理合约选择器变更失败: {newContract}");
            }
        }

        /// <summary>
        /// 更新合约显示
        /// </summary>
        private void UpdateContractDisplay(string contractCode)
        {
            try
            {
                // 更新期货价格显示区域的合约信息
                Dispatcher.Invoke(() =>
                {
                    // 更新UI中显示当前合约的地方
                    var displayName = GetContractDisplayName(contractCode);
                    _logger.LogDebug($"UI合约显示已更新为: {contractCode} ({displayName})");

                    // 这里可以添加更多UI更新逻辑
                    // 例如更新状态栏、标题栏等显示当前合约的地方
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新合约显示失败: {contractCode}");
            }
        }

        /// <summary>
        /// 获取合约显示名称
        /// </summary>
        private string GetContractDisplayName(string contractCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(contractCode))
                {
                    return "未知合约";
                }

                if (contractCode.StartsWith("SHFE.au"))
                {
                    var monthCode = contractCode.Substring(7); // 提取月份代码，如"2510"
                    return $"沪金{monthCode}";
                }

                return contractCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取合约显示名称失败: {contractCode}");
                return contractCode;
            }
        }

        #region 启动画面控制

        /// <summary>
        /// 启动加载动画
        /// </summary>
        private void StartLoadingAnimation()
        {
            try
            {
                // 创建加载点的动画
                var storyboard = new Storyboard();
                storyboard.RepeatBehavior = RepeatBehavior.Forever;

                // 为每个加载点创建缩放动画
                for (int i = 1; i <= 3; i++)
                {
                    var scaleTransform = FindName($"LoadingDot{i}Scale") as ScaleTransform;
                    if (scaleTransform != null)
                    {
                        var scaleAnimation = new DoubleAnimationUsingKeyFrames();
                        scaleAnimation.KeyFrames.Add(new EasingDoubleKeyFrame(1.0, KeyTime.FromTimeSpan(TimeSpan.FromSeconds(0))));
                        scaleAnimation.KeyFrames.Add(new EasingDoubleKeyFrame(1.5, KeyTime.FromTimeSpan(TimeSpan.FromSeconds(0.2 + i * 0.1))));
                        scaleAnimation.KeyFrames.Add(new EasingDoubleKeyFrame(1.0, KeyTime.FromTimeSpan(TimeSpan.FromSeconds(0.4 + i * 0.1))));
                        scaleAnimation.KeyFrames.Add(new EasingDoubleKeyFrame(1.0, KeyTime.FromTimeSpan(TimeSpan.FromSeconds(1.2))));

                        Storyboard.SetTarget(scaleAnimation, scaleTransform);
                        Storyboard.SetTargetProperty(scaleAnimation, new PropertyPath(ScaleTransform.ScaleXProperty));
                        storyboard.Children.Add(scaleAnimation);

                        var scaleAnimationY = scaleAnimation.Clone();
                        Storyboard.SetTargetProperty(scaleAnimationY, new PropertyPath(ScaleTransform.ScaleYProperty));
                        storyboard.Children.Add(scaleAnimationY);
                    }
                }

                storyboard.Begin();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动加载动画失败");
            }
        }

        /// <summary>
        /// 更新加载状态文本
        /// </summary>
        /// <param name="status">状态文本</param>
        private void UpdateLoadingStatus(string status)
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (LoadingStatusText != null)
                    {
                        LoadingStatusText.Text = status;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新加载状态失败: {Status}", status);
            }
        }

        /// <summary>
        /// 隐藏启动画面
        /// </summary>
        private void HideSplashScreen()
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (SplashScreenOverlay != null)
                    {
                        _logger.LogInformation("🎬 开始启动画面淡出动画...");

                        // 创建更平滑的淡出动画
                        var fadeOut = new DoubleAnimation
                        {
                            From = 1.0,
                            To = 0.0,
                            Duration = TimeSpan.FromMilliseconds(800), // 稍微延长动画时间
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
                        };

                        fadeOut.Completed += (s, e) =>
                        {
                            try
                            {
                                SplashScreenOverlay.Visibility = Visibility.Collapsed;
                                _logger.LogInformation("✅ 启动画面已完全隐藏");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "❌ 设置启动画面可见性失败");
                            }
                        };

                        SplashScreenOverlay.BeginAnimation(UIElement.OpacityProperty, fadeOut);
                        _logger.LogInformation("🎬 启动画面淡出动画已开始");
                    }
                    else
                    {
                        _logger.LogWarning("⚠️ SplashScreenOverlay为null，无法执行淡出动画");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 隐藏启动画面失败，尝试强制隐藏");
                // 如果动画失败，直接隐藏
                try
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        if (SplashScreenOverlay != null)
                        {
                            SplashScreenOverlay.Visibility = Visibility.Collapsed;
                            _logger.LogInformation("✅ 启动画面已强制隐藏");
                        }
                    });
                }
                catch (Exception innerEx)
                {
                    _logger.LogError(innerEx, "❌ 强制隐藏启动画面也失败");
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region 消息处理

        /// <summary>
        /// 处理删除持仓订单消息
        /// </summary>
        /// <param name="message">删除订单消息</param>
        public void Receive(DeletePositionOrderMessage message)
        {
            try
            {
                _logger.LogInformation("收到删除持仓订单消息: {OrderId}", message.OrderId);

                // 调用删除订单的处理方法
                _ = Task.Run(async () => await HandleDeletePositionOrder(message.OrderId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理删除持仓订单消息时发生错误");
            }
        }

        /// <summary>
        /// 处理平仓订单消息（已弃用 - 现在使用新的直接启动流程）
        /// </summary>
        /// <param name="message">平仓订单消息</param>
        public void Receive(ClosePositionOrderMessage message)
        {
            try
            {
                if (message.CloseRequest is CloseRequest closeRequest)
                {
                    _logger.LogInformation("⚠️ 收到旧的平仓订单消息（已弃用）: OrderId={OrderId}, Platform={Platform}",
                        closeRequest.OrderId, closeRequest.PlatformId);

                    // 🚀 旧的消息传递方式已被弃用
                    // 新的平仓流程直接在 PositionOrderCard.ClosePositionButton_Click 中启动
                    // 无需通过消息传递，避免了额外的弹窗和确认步骤
                    _logger.LogWarning("平仓消息传递方式已弃用，新流程直接启动两段式平仓");
                }
                else
                {
                    _logger.LogWarning("平仓消息格式不正确");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理旧平仓订单消息时发生错误");
            }
        }

        /// <summary>
        /// 启动平仓任务（已弃用 - 现在直接在 PositionOrderCard 中启动）
        /// </summary>
        /// <param name="closeRequest">平仓请求</param>
        [Obsolete("此方法已弃用，新的平仓流程直接在 PositionOrderCard.ClosePositionButton_Click 中启动")]
        private async Task StartClosePositionTask(CloseRequest closeRequest)
        {
            try
            {
                _logger.LogWarning("⚠️ 使用了已弃用的 StartClosePositionTask 方法: OrderId={OrderId}", closeRequest.OrderId);

                // 🚀 新的平仓流程已经直接在 PositionOrderCard 中启动
                // 无需通过 MainWindow 的消息处理，避免了额外的弹窗和确认步骤
                _logger.LogInformation("新的平仓流程直接启动，无需通过消息传递");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理已弃用的平仓任务方法时发生错误: OrderId={OrderId}", closeRequest.OrderId);
            }
        }

        /// <summary>
        /// 期货正向开仓按钮点击事件 - 修改为单期货交易
        /// </summary>
        private async void FuturesForwardTradeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("🚀 用户点击期货正向开仓按钮(单期货模式)");

                // 显示操作提示
                ShowOperationMessage("🔄", "正在执行期货正向开仓...", "#007bff", "#cce7ff");

                // 禁用按钮防止重复点击
                if (sender is Button button)
                {
                    button.IsEnabled = false;
                }

                // 获取单期货交易服务
                var app = (App)Application.Current;
                var serviceProvider = app.ServiceProvider;
                var singleFuturesService = serviceProvider.GetRequiredService<SingleFuturesTradingService>();

                // 执行单期货正向开仓 (direction = 1 表示买涨)
                var result = await singleFuturesService.ExecuteSingleFuturesOpenAsync(direction: 1, userId: 1);

                if (result.Success)
                {
                    _logger.LogInformation("✅ 期货正向开仓成功: OrderId={OrderId}, InternalOrderId={InternalOrderId}",
                        result.OrderId, result.InternalOrderId);

                    ShowOperationMessage("✅",
                        $"期货正向开仓成功！\n订单号: {result.OrderId}\n成交价: {result.ExecutedPrice:F2}\n手数: {result.ExecutedVolume}",
                        "#28a745", "#d4edda");
                }
                else
                {
                    _logger.LogError("❌ 期货正向开仓失败: {Message}", result.Message);
                    ShowOperationMessage("❌", $"期货正向开仓失败: {result.Message}", "#dc3545", "#f8d7da");
                }

                // 3秒后隐藏提示
                HideOperationMessageAfterDelay(3000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "期货正向开仓异常");
                ShowOperationMessage("❌", $"期货正向开仓异常: {ex.Message}", "#dc3545", "#f8d7da");
                HideOperationMessageAfterDelay(5000);
            }
            finally
            {
                // 重新启用按钮
                if (sender is Button btn)
                {
                    btn.IsEnabled = true;
                }
            }
        }

        /// <summary>
        /// 期货反向开仓按钮点击事件 - 修改为单期货交易
        /// </summary>
        private async void FuturesReverseTradeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("🚀 用户点击期货反向开仓按钮(单期货模式)");

                // 显示操作提示
                ShowOperationMessage("🔄", "正在执行期货反向开仓...", "#007bff", "#cce7ff");

                // 禁用按钮防止重复点击
                if (sender is Button button)
                {
                    button.IsEnabled = false;
                }

                // 获取单期货交易服务
                var app = (App)Application.Current;
                var serviceProvider = app.ServiceProvider;
                var singleFuturesService = serviceProvider.GetRequiredService<SingleFuturesTradingService>();

                // 执行单期货反向开仓 (direction = -1 表示买跌)
                var result = await singleFuturesService.ExecuteSingleFuturesOpenAsync(direction: -1, userId: 1);

                if (result.Success)
                {
                    _logger.LogInformation("✅ 期货反向开仓成功: OrderId={OrderId}, InternalOrderId={InternalOrderId}",
                        result.OrderId, result.InternalOrderId);

                    ShowOperationMessage("✅",
                        $"期货反向开仓成功！\n订单号: {result.OrderId}\n成交价: {result.ExecutedPrice:F2}\n手数: {result.ExecutedVolume}",
                        "#28a745", "#d4edda");
                }
                else
                {
                    _logger.LogError("❌ 期货反向开仓失败: {Message}", result.Message);
                    ShowOperationMessage("❌", $"期货反向开仓失败: {result.Message}", "#dc3545", "#f8d7da");
                }

                // 3秒后隐藏提示
                HideOperationMessageAfterDelay(3000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "期货反向开仓异常");
                ShowOperationMessage("❌", $"期货反向开仓异常: {ex.Message}", "#dc3545", "#f8d7da");
                HideOperationMessageAfterDelay(5000);
            }
            finally
            {
                // 重新启用按钮
                if (sender is Button btn)
                {
                    btn.IsEnabled = true;
                }
            }
        }



        #endregion

        #region 🚀 期货配置相关方法

        /// <summary>
        /// 期货密码框密码变更事件
        /// </summary>
        private void FuturesPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is PasswordBox passwordBox)
                {
                    FuturesConfig.Password = passwordBox.Password;
                    _logger.LogDebug("期货密码已更新");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理期货密码变更失败");
            }
        }

        /// <summary>
        /// 保存期货配置按钮点击事件
        /// </summary>
        private async void SaveFuturesConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInformation("用户点击保存期货配置按钮");

                // 禁用保存按钮防止重复点击
                if (sender is Button saveButton)
                {
                    saveButton.IsEnabled = false;
                    saveButton.Content = "保存中...";
                }

                // 显示保存中状态
                ShowOperationMessage("⏳", "正在保存期货配置...", "#17a2b8", "#d1ecf1");

                // 验证输入
                if (string.IsNullOrWhiteSpace(FuturesConfig.PlatformName))
                {
                    ShowOperationMessage("❌", "平台名称不能为空", "#dc3545", "#f8d7da");
                    return;
                }

                if (string.IsNullOrWhiteSpace(FuturesConfig.Username))
                {
                    ShowOperationMessage("❌", "账号不能为空", "#dc3545", "#f8d7da");
                    return;
                }

                if (string.IsNullOrWhiteSpace(FuturesConfig.Password))
                {
                    ShowOperationMessage("❌", "密码不能为空", "#dc3545", "#f8d7da");
                    return;
                }

                // 保存到数据库
                bool saveSuccess = await SaveFuturesConfigToDatabaseAsync();

                if (saveSuccess)
                {
                    ShowOperationMessage("✅", "期货配置保存成功", "#28a745", "#d4edda");
                    _logger.LogInformation("期货配置保存成功");
                }
                else
                {
                    ShowOperationMessage("❌", "期货配置保存失败", "#dc3545", "#f8d7da");
                    _logger.LogError("期货配置保存失败");
                }

                // 3秒后隐藏提示
                HideOperationMessageAfterDelay(3000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存期货配置时发生错误");
                ShowOperationMessage("❌", $"保存失败: {ex.Message}", "#dc3545", "#f8d7da");
                HideOperationMessageAfterDelay(5000);
            }
            finally
            {
                // 恢复保存按钮状态
                if (sender is Button saveButton)
                {
                    saveButton.IsEnabled = true;
                    saveButton.Content = "保存配置";
                }
            }
        }

        /// <summary>
        /// 保存期货配置到数据库
        /// </summary>
        private async Task<bool> SaveFuturesConfigToDatabaseAsync()
        {
            try
            {
                // 使用期货账号配置服务保存到数据库
                var result = await _futuresAccountConfigService.UpdateFuturesAccountAsync(
                    1, // 默认用户ID
                    FuturesConfig.PlatformName,
                    FuturesConfig.Username,
                    FuturesConfig.Password,
                    FuturesConfig.BrokerId);

                if (result)
                {
                    _logger.LogInformation("期货配置已保存到数据库: 平台={Platform}, 账号={Username}",
                        FuturesConfig.PlatformName, FuturesConfig.Username);
                }
                else
                {
                    _logger.LogError("期货配置保存到数据库失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存期货配置到数据库失败");
                return false;
            }
        }

        /// <summary>
        /// 加载期货配置
        /// </summary>
        private async Task LoadFuturesConfigAsync()
        {
            try
            {
                _logger.LogInformation("开始从数据库加载期货配置");

                // 从数据库加载期货配置
                var config = await _futuresAccountConfigService.GetUserFuturesAccountAsync(1); // 默认用户ID

                if (config != null)
                {
                    FuturesConfig.PlatformName = config.PlatformName;
                    FuturesConfig.Username = config.Username;
                    FuturesConfig.BrokerId = config.BrokerId;

                    // 解密密码
                    if (!string.IsNullOrEmpty(config.PasswordEncrypted))
                    {
                        var decryptedPassword = ((FuturesAccountConfigService)_futuresAccountConfigService).DecryptPassword(config.PasswordEncrypted);
                        FuturesConfig.Password = decryptedPassword;
                    }
                    else
                    {
                        FuturesConfig.Password = "";
                    }

                    _logger.LogInformation("期货配置加载成功: 平台={Platform}, 账号={Username}",
                        config.PlatformName, config.Username);
                }
                else
                {
                    // 设置用户实际的默认值
                    FuturesConfig.PlatformName = "S上海东亚ctp主席";
                    FuturesConfig.Username = "801265";
                    FuturesConfig.Password = "";
                    FuturesConfig.BrokerId = "S上海东亚ctp主席";

                    _logger.LogInformation("未找到期货配置，使用用户实际默认值: 平台={Platform}, 账号={Username}",
                        FuturesConfig.PlatformName, FuturesConfig.Username);
                }

                // 更新密码框
                if (FuturesPasswordBox != null)
                {
                    FuturesPasswordBox.Password = FuturesConfig.Password;
                }

                _logger.LogInformation("期货配置加载完成: 最终值 - 平台={Platform}, 账号={Username}",
                    FuturesConfig.PlatformName, FuturesConfig.Username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载期货配置失败");

                // 出错时设置用户实际的默认值
                FuturesConfig.PlatformName = "S上海东亚ctp主席";
                FuturesConfig.Username = "801265";
                FuturesConfig.Password = "";
                FuturesConfig.BrokerId = "S上海东亚ctp主席";
            }
        }



        /// <summary>
        /// 初始化ViewModels（不使用重试机制）
        /// </summary>
        private async Task InitializeViewModelsAsync()
        {
            _logger.LogInformation("🎨 开始初始化ViewModels...");

            // 🔧 等待关键服务准备就绪
            await WaitForCriticalServicesAsync();

            var app = (App)Application.Current;
            var serviceProvider = app.ServiceProvider;

            // 🔧 在后台线程获取ViewModels，避免阻塞UI线程
            SpotPlatformMarketViewModel spotPlatformMarket = null;
            PriceDisplayViewModel priceDisplayViewModel = null;
            TradeStatusMonitorViewModel tradeStatusMonitorViewModel = null;

            // 分别尝试获取每个ViewModel，记录详细的错误信息
            try
            {
                spotPlatformMarket = serviceProvider.GetRequiredService<SpotPlatformMarketViewModel>();
                _logger.LogInformation("✅ SpotPlatformMarketViewModel获取成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ SpotPlatformMarketViewModel获取失败: {Message}", ex.Message);
                throw;
            }

            try
            {
                priceDisplayViewModel = serviceProvider.GetRequiredService<PriceDisplayViewModel>();
                _logger.LogInformation("✅ PriceDisplayViewModel获取成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ PriceDisplayViewModel获取失败: {Message}", ex.Message);
                throw;
            }

            try
            {
                tradeStatusMonitorViewModel = serviceProvider.GetRequiredService<TradeStatusMonitorViewModel>();
                _logger.LogInformation("✅ TradeStatusMonitorViewModel获取成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ TradeStatusMonitorViewModel获取失败: {Message}", ex.Message);
                throw;
            }

            // 🔧 在UI线程上设置属性
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                SpotPlatformMarket = spotPlatformMarket;
                PriceDisplayViewModel = priceDisplayViewModel;
                TradeStatusMonitorViewModel = tradeStatusMonitorViewModel;

                // 验证SpotPlatformMarket初始化
                _logger.LogInformation("🔍 SpotPlatformMarket初始化验证:");
                _logger.LogInformation("  - SpotPlatformMarket是否为null: {IsNull}", SpotPlatformMarket == null);
                if (SpotPlatformMarket != null)
                {
                    _logger.LogInformation("  - ForwardTradeCommand是否为null: {IsNull}", SpotPlatformMarket.ForwardTradeCommand == null);
                    _logger.LogInformation("  - ReverseTradeCommand是否为null: {IsNull}", SpotPlatformMarket.ReverseTradeCommand == null);
                }

                // 设置DataContext
                this.DataContext = this;
                _logger.LogInformation("🔍 DataContext已设置为MainWindow实例");

                // 设置交易监控面板的DataContext
                if (TradeStatusMonitor != null)
                {
                    TradeStatusMonitor.DataContext = TradeStatusMonitorViewModel;
                }

                _logger.LogInformation("✅ ViewModels初始化成功");
            });
        }

        /// <summary>
        /// 等待关键服务准备就绪（只检查启动必需的核心服务）
        /// </summary>
        private async Task WaitForCriticalServicesAsync()
        {
            const int maxWaitTime = 5000; // 减少等待时间到5秒
            const int checkInterval = 200; // 每200ms检查一次
            int elapsedTime = 0;

            _logger.LogInformation("🔍 开始等待关键服务准备就绪...");

            while (elapsedTime < maxWaitTime)
            {
                try
                {
                    var app = (App)Application.Current;
                    var serviceProvider = app.ServiceProvider;

                    _logger.LogInformation("🔍 检查服务提供者: {ServiceProvider}", serviceProvider != null);

                    // 只检查启动必需的核心服务（移除TradeTaskManager以避免启动阻塞）
                    int availableServices = 0;

                    // 检查 MultiPlatformSpotPriceService
                    try
                    {
                        var multiPlatformService = serviceProvider.GetService<IMultiPlatformSpotPriceService>();
                        if (multiPlatformService != null) availableServices++;
                        _logger.LogInformation("🔍 MultiPlatformSpotPriceService: {Available}", multiPlatformService != null);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("MultiPlatformSpotPriceService检查异常: {Message}", ex.Message);
                    }

                    // 检查 BasisExtremesService
                    try
                    {
                        var basisExtremesService = serviceProvider.GetService<IBasisExtremesService>();
                        if (basisExtremesService != null) availableServices++;
                        _logger.LogInformation("🔍 BasisExtremesService: {Available}", basisExtremesService != null);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("BasisExtremesService检查异常: {Message}", ex.Message);
                    }

                    // 检查 BasisDataAggregationService
                    try
                    {
                        var basisAggregationService = serviceProvider.GetService<BasisDataAggregationService>();
                        if (basisAggregationService != null) availableServices++;
                        _logger.LogInformation("🔍 BasisDataAggregationService: {Available}", basisAggregationService != null);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("BasisDataAggregationService检查异常: {Message}", ex.Message);
                    }

                    // 🔧 只检查核心服务：MultiPlatformSpotPriceService、BasisExtremesService、BasisDataAggregationService
                    // TradeTaskManager不是启动必需的，移除其检查以避免启动阻塞
                    if (availableServices >= 3)
                    {
                        _logger.LogInformation("✅ 关键服务准备就绪，耗时: {ElapsedTime}ms，可用服务数: {AvailableServices}/3",
                            elapsedTime, availableServices);
                        return;
                    }

                    _logger.LogInformation("⏳ 等待服务准备，可用数={AvailableServices}/3 (需要3个核心服务)", availableServices);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("服务检查异常: {Message}", ex.Message);
                    _logger.LogDebug(ex, "服务检查异常详情");
                }

                await Task.Delay(checkInterval);
                elapsedTime += checkInterval;
            }

            // 如果超时，记录警告但继续执行
            _logger.LogWarning("⚠️ 关键服务等待超时 ({MaxWaitTime}ms)，继续执行ViewModels初始化", maxWaitTime);
        }

        /// <summary>
        /// 等待ViewModels初始化完成
        /// </summary>
        private async Task WaitForViewModelsInitializationAsync()
        {
            const int maxWaitTime = 30000; // 最大等待30秒
            const int checkInterval = 100; // 每100ms检查一次
            int elapsedTime = 0;

            while (elapsedTime < maxWaitTime)
            {
                // 检查关键的ViewModels是否已经初始化
                if (SpotPlatformMarket != null &&
                    PriceDisplayViewModel != null &&
                    TradeStatusMonitorViewModel != null)
                {
                    _logger.LogInformation("✅ ViewModels初始化检查通过，耗时: {ElapsedTime}ms", elapsedTime);
                    return;
                }

                await Task.Delay(checkInterval);
                elapsedTime += checkInterval;
            }

            // 如果超时，记录警告但继续执行
            _logger.LogWarning("⚠️ ViewModels初始化等待超时 ({MaxWaitTime}ms)，继续执行", maxWaitTime);
            _logger.LogWarning("ViewModels状态: SpotPlatformMarket={SpotPlatformMarket}, PriceDisplayViewModel={PriceDisplayViewModel}, TradeStatusMonitorViewModel={TradeStatusMonitorViewModel}",
                SpotPlatformMarket != null, PriceDisplayViewModel != null, TradeStatusMonitorViewModel != null);
        }

        #endregion
    }
}