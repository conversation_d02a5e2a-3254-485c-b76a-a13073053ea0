2025-08-19 16:24:59.272 +08:00 [INF] 路径解析服务初始化完成
2025-08-19 16:24:59.306 +08:00 [INF] 应用程序根目录: H:\gold\gold\package-distribution-improved
2025-08-19 16:24:59.306 +08:00 [INF] 便携模式: true
2025-08-19 16:24:59.306 +08:00 [INF] 开始验证和修复配置...
2025-08-19 16:24:59.306 +08:00 [INF] 已修复的配置问题:
2025-08-19 16:24:59.307 +08:00 [INF]   ✅ 数据库连接字符串需要更新为: Data Source=H:\gold\gold\package-distribution-improved\data\goldarbitrage_unified.db
2025-08-19 16:24:59.307 +08:00 [INF] 配置验证完成，成功: true
2025-08-19 16:24:59.307 +08:00 [INF] 开始初始化环境变量...
2025-08-19 16:24:59.307 +08:00 [INF] 应用程序环境变量设置完成
2025-08-19 16:24:59.307 +08:00 [INF] 添加TqSdk服务目录到PYTHONPATH: H:\gold\gold\package-distribution-improved\app\TqSdkPythonService
2025-08-19 16:24:59.307 +08:00 [INF] Python环境变量设置完成
2025-08-19 16:24:59.367 +08:00 [INF] TqSdk环境变量设置完成
2025-08-19 16:24:59.368 +08:00 [INF] Playwright环境变量设置完成
2025-08-19 16:24:59.368 +08:00 [INF] 环境变量初始化完成
2025-08-19 16:24:59.374 +08:00 [INF] 数据流初始化完成
2025-08-19 16:24:59.377 +08:00 [INF] 数据流服务启动
2025-08-19 16:24:59.380 +08:00 [INF] 内存管理服务启动
2025-08-19 16:24:59.385 +08:00 [INF] 开始基差极值服务初始化
2025-08-19 16:24:59.419 +08:00 [INF] 开始基差极值数据迁移：从JSON文件到数据库
2025-08-19 16:24:59.436 +08:00 [INF] JSON文件已备份: H:\gold\gold\package-distribution-improved\app\Data\basis_extremes.json.backup_20250819_162459
2025-08-19 16:24:59.436 +08:00 [INF] 基差极值数据迁移完成: 迁移完成：成功迁移 0 条记录，跳过 0 条记录
2025-08-19 16:24:59.436 +08:00 [INF] 数据迁移完成: 迁移完成：成功迁移 0 条记录，跳过 0 条记录
2025-08-19 16:24:59.587 +08:00 [INF] 基差极值数据统计: 总记录=140, 平台数=4, 最后更新="2025-08-19T15:42:42.9952283"
2025-08-19 16:24:59.597 +08:00 [INF] 基差极值服务初始化完成
2025-08-19 16:24:59.598 +08:00 [INF] 基差数据聚合服务已启动
2025-08-19 16:24:59.599 +08:00 [INF] === 开始初始化Mock服务 ===
2025-08-19 16:24:59.599 +08:00 [INF] === Mock配置状态检查 ===
2025-08-19 16:24:59.599 +08:00 [INF] 📋 Mock启用状态: ❌ 未启用
2025-08-19 16:24:59.599 +08:00 [WRN] ⚠️ Mock模式未启用，应用将尝试使用真实API
2025-08-19 16:24:59.599 +08:00 [INF] === Mock配置检查完成 ===
2025-08-19 16:24:59.599 +08:00 [INF] === Mock服务依赖验证 ===
2025-08-19 16:24:59.599 +08:00 [INF] MockConfiguration: ✅ 已注册
2025-08-19 16:24:59.604 +08:00 [INF] 🔧 Mock数据服务已初始化，已订阅用户配置变更事件
2025-08-19 16:24:59.604 +08:00 [INF] IMockDataService: ✅ 已注册
2025-08-19 16:24:59.604 +08:00 [INF] 合约管理服务初始化完成，默认合约: SHFE.au2510
2025-08-19 16:24:59.604 +08:00 [INF] 期货价格服务初始化完成，离线模式: False，数据文件路径: H:\gold\gold\package-distribution-improved\app\futures_data.json，天勤SDK脚本路径: H:\gold\gold\package-distribution-improved\app\TqSdkPythonService\tqsdk_futures_service.py，当前合约: SHFE.au2510
2025-08-19 16:24:59.605 +08:00 [INF] Mock服务适配器已初始化，当前模式: Real
2025-08-19 16:24:59.605 +08:00 [INF] MockServiceAdapter: ✅ 已注册
2025-08-19 16:24:59.605 +08:00 [INF] MockServiceInitializer: ✅ 已注册
2025-08-19 16:24:59.605 +08:00 [INF] === Mock服务依赖验证完成 ===
2025-08-19 16:24:59.605 +08:00 [INF] ✅ MockServiceInitializer已获取，开始初始化...
2025-08-19 16:24:59.605 +08:00 [INF] === Mock服务初始化检查 ===
2025-08-19 16:24:59.605 +08:00 [INF] Mock模式启用状态: False
2025-08-19 16:24:59.605 +08:00 [WRN] ⚠️ Mock模式未启用，跳过Mock服务初始化
2025-08-19 16:24:59.605 +08:00 [INF] 🎉 Mock服务初始化完成！
2025-08-19 16:24:59.605 +08:00 [INF] 🖥️ 开始创建并显示主窗口...
2025-08-19 16:24:59.605 +08:00 [INF] 🔧 开始创建MainWindow实例...
2025-08-19 16:24:59.610 +08:00 [INF] 🔍 应用程序根目录检测:
2025-08-19 16:24:59.610 +08:00 [INF]   程序集位置: H:\gold\gold\package-distribution-improved\app\GoldArbitrageDesktop.Core.dll
2025-08-19 16:24:59.610 +08:00 [INF]   程序目录: H:\gold\gold\package-distribution-improved\app
2025-08-19 16:24:59.610 +08:00 [INF]   目录名称: app
2025-08-19 16:24:59.610 +08:00 [INF]   检测到app目录，向上一级: H:\gold\gold\package-distribution-improved
2025-08-19 16:24:59.610 +08:00 [INF] 环境感知配置管理器初始化完成
2025-08-19 16:24:59.610 +08:00 [INF] 应用程序根目录: H:\gold\gold\package-distribution-improved
2025-08-19 16:24:59.610 +08:00 [INF] 便携模式: true
2025-08-19 16:24:59.612 +08:00 [INF] 从配置文件确定环境: "Production"
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter构造函数开始...
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: IFuturesAccountConfigService设置完成
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: HttpClient设置完成
2025-08-19 16:24:59.613 +08:00 [INF] [A/B] Adapter ctor reached v2025-08-09-AB1, BaseDir=H:\gold\gold\package-distribution-improved\app\
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: 开始设置Python服务路径...
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: Python服务路径设置完成
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: 开始设置统一服务路径...
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: 统一服务路径设置完成
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: 开始配置HttpClient...
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter: HttpClient配置完成
2025-08-19 16:24:59.613 +08:00 [INF] 天勤SDK交易适配器已创建
2025-08-19 16:24:59.613 +08:00 [INF] Python服务路径: H:\gold\gold\package-distribution-improved\app\TqSdkPythonService\tqsdk_trading_http_server.py
2025-08-19 16:24:59.613 +08:00 [INF] 统一服务路径: H:\gold\gold\package-distribution-improved\app\TqSdkPythonService\unified_tqsdk_service.py
2025-08-19 16:24:59.613 +08:00 [INF] 交易服务器: http://127.0.0.1:8889
2025-08-19 16:24:59.613 +08:00 [INF] 🔧 [DEBUG] TqSdkTradingAdapter构造函数完成
2025-08-19 16:24:59.614 +08:00 [INF] 🔧 [DEBUG] FuturesTradingService构造函数开始...
2025-08-19 16:24:59.614 +08:00 [INF] 🔧 [DEBUG] FuturesTradingService: TqSdkTradingAdapter设置完成
2025-08-19 16:24:59.614 +08:00 [INF] 🔧 [DEBUG] FuturesTradingService构造函数完成
2025-08-19 16:24:59.614 +08:00 [INF] 合约选择器ViewModel构造函数完成，默认合约: SHFE.au2510
2025-08-19 16:24:59.615 +08:00 [INF] 🔧 MainWindow构造函数开始...
2025-08-19 16:25:00.045 +08:00 [INF] 🔧 InitializeComponent完成
2025-08-19 16:25:00.046 +08:00 [INF] 🔧 开始验证contractSelectorViewModel参数...
2025-08-19 16:25:00.046 +08:00 [INF] 🔧 所有依赖注入参数验证完成
2025-08-19 16:25:00.046 +08:00 [INF] 🔧 准备注册Loaded事件...
2025-08-19 16:25:00.046 +08:00 [INF] 🔧 Loaded事件注册完成
2025-08-19 16:25:00.046 +08:00 [INF] ✅ 主窗口构造函数完成，等待窗口加载事件进行完整初始化
2025-08-19 16:25:00.046 +08:00 [INF] ✅ MainWindow实例已创建
2025-08-19 16:25:00.046 +08:00 [INF] 🔧 准备配置主窗口...
2025-08-19 16:25:00.253 +08:00 [INF] 🚀 窗口已加载，开始完整初始化流程...
2025-08-19 16:25:00.253 +08:00 [INF] 🔧 开始初始化其他服务...
2025-08-19 16:25:00.253 +08:00 [INF] 🔧 [DEBUG] 步骤1: 获取serviceProvider完成
2025-08-19 16:25:00.253 +08:00 [INF] 📊 使用真实数据服务
2025-08-19 16:25:00.253 +08:00 [INF] 🔧 [DEBUG] 步骤2.1: 开始获取PositionOrderRealTimeService...
2025-08-19 16:25:00.253 +08:00 [INF] ✅ 持仓订单实时更新服务已初始化
2025-08-19 16:25:00.253 +08:00 [INF] 🔧 [DEBUG] 步骤2.2: PositionOrderRealTimeService获取完成
2025-08-19 16:25:00.254 +08:00 [INF] 🔧 [DEBUG] 步骤2.3: 开始获取PositionOrdersManagerViewModel...
2025-08-19 16:25:00.270 +08:00 [INF] 已加载持久化状态: Count=21
2025-08-19 16:25:00.271 +08:00 [INF] 交易状态管理器已初始化: PersistenceFile=C:\Users\<USER>\AppData\Local\Temp\GoldArbitrage\trading_states.json
